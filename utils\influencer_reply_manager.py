import tweepy
import asyncio
from datetime import datetime

from utils.database_utils import save_to_db, check_replied_tweet
from utils.inference_utils import generate_post
from utils.logger_utils import setup_logger

logger = setup_logger(name="influencer_reply_manager", log_file="log/app.log")

class InfluencerReplyManager:
    def __init__(self, influencer):
        self.influencer = influencer

    async def save_replied_tweet(self, mention, message, error=False):
        await save_to_db(
            table_name='replied_tweets',
            content={
                'project_id': self.influencer.project_id,
                'message': message,
                'user_id': getattr(mention, 'author_id', None),
                'conversation_id': getattr(mention, 'conversation_id', None),
                'reply_time': int(datetime.now().timestamp()),
                'tweet_id': 'error' if error else getattr(mention, 'id', None),
            }
        )

    async def get_reply_content(self, mentioned_tweet_text: str):
        try:
            self.influencer.initialize_llm()
            cleaned_text = ' '.join(word for word in mentioned_tweet_text.split() if not word.startswith('@'))
            # Compose the prompt dynamically from the influencer's user prompt and the tweet text
            user_prompt = f"{self.influencer.llm_user_prompt}\n\nOriginal tweet: {cleaned_text}"
            check, reply = await generate_post(
                user_id=self.influencer.project_id,
                llm=self.influencer.llm,
                tweets=cleaned_text,
                max_tokens=self.influencer.llm_max_tokens,
                temperature=self.influencer.llm_temperature,
                top_p=self.influencer.llm_top_p,
                system_prompt=self.influencer.llm_system_prompt,
                prompt=user_prompt
            )
            if check:
                cleaned_reply = ' '.join(word for word in reply.split() if not word.startswith('@'))
                return True, cleaned_reply.strip()
            else:
                return False, reply
        except Exception as e:
            logger.error(f"Error generating reply content for influencer {self.influencer.project_id}: {e}")
            return False, str(e)

    async def process_single_mention(self, mention):
        try:
            if mention.referenced_tweets and any(ref.type == 'replied_to' and ref.id == self.influencer.my_user_id for ref in mention.referenced_tweets):
                logger.info(f"Influencer {self.influencer.project_id}: Skipping self-reply tweet {mention.id}")
                return
            try:
                has_replied = await check_replied_tweet(self.influencer.project_id, mention.id)
                if has_replied:
                    logger.info(f"Influencer {self.influencer.project_id}: Skipping already replied tweet {mention.id}")
                    return
            except Exception as e:
                logger.error(f"Influencer {self.influencer.project_id}: Error checking replied tweets in database: {e}")
                await self.save_replied_tweet(mention, f"Error checking replied tweets: {e}", error=True)
                return
            if mention.in_reply_to_user_id is None or mention.in_reply_to_user_id == self.influencer.my_user_id:
                original_tweet_text = mention.text
                if mention.referenced_tweets:
                    for ref in mention.referenced_tweets:
                        if ref.type == 'replied_to':
                            try:
                                # Acquire rate limit slot before fetching original tweet
                                if self.influencer.rate_limiter:
                                    await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "get_tweet")
                                original_tweet = self.influencer.client.get_tweet(
                                    id=ref.id,
                                    tweet_fields=["text", "author_id"]
                                )
                                if original_tweet.data:
                                    original_tweet_text = f"Original tweet: {original_tweet.data.text}\nReply: {mention.text}"
                                    logger.info(f"Fetched original tweet for context")
                            except tweepy.TooManyRequests:
                                logger.warning(f"Rate limit hit while fetching original tweet. Sleeping for 15 minutes.")
                                await self.save_replied_tweet(mention, "Rate limit hit while fetching original tweet.", error=True)
                                await asyncio.sleep(15 * 60)
                                return
                            except Exception as e:
                                logger.error(f"Error fetching original tweet: {e}")
                                await self.save_replied_tweet(mention, f"Error fetching original tweet: {e}", error=True)
                                pass
                success, reply_text = await self.get_reply_content(original_tweet_text)
                if not success:
                    logger.error(f"Error generating reply: {reply_text}")
                    await self.save_replied_tweet(mention, f"Error generating reply: {reply_text}", error=True)
                    return
                try:
                    # Ensure client is initialized
                    if self.influencer.client is None:
                        if self.influencer.client_init_task is not None:
                            if not self.influencer.client_init_task.done():
                                logger.info(f"Waiting for client initialization for influencer {self.influencer.project_id}")
                                await self.influencer.client_init_task
                        else:
                            logger.info(f"Initializing client for influencer {self.influencer.project_id}")
                            await self.influencer.initialize_client()
                    
                    # Acquire rate limit slot before replying
                    if self.influencer.rate_limiter:
                        await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "create_tweet")
                    
                    self.influencer.client.create_tweet(text=reply_text, in_reply_to_tweet_id=mention.id)
                    logger.info(f"Replied to mention {mention.id} by {mention.author_id}")
                    await self.save_replied_tweet(mention, f"Replied under https://x.com/{self.influencer.x_username}/status/{mention.conversation_id}")
                    if self.influencer.last_mention_id is None or mention.id > self.influencer.last_mention_id:
                        self.influencer.last_mention_id = mention.id
                        await self.influencer.update_state()
                except tweepy.TooManyRequests:
                    logger.warning(f"Rate limit hit while replying to tweet {mention.id}. Sleeping for 15 minutes.")
                    await self.save_replied_tweet(mention, "Rate limit hit while replying to tweet.", error=True)
                    await asyncio.sleep(15 * 60)
                    return
                except Exception as e:
                    logger.error(f"Error replying to tweet {mention.id}: {e}")
                    await self.save_replied_tweet(mention, f"Error replying to tweet: {e}", error=True)
        except Exception as e:
            logger.error(f"Error processing mention {mention.id} for influencer {self.influencer.project_id}: {e}")
            await self.save_replied_tweet(mention, f"Error processing mention: {e}", error=True)

    async def process_mentions(self, current_time):
        try:
            current_hour = current_time.hour
            if self.influencer.active_reply_hours_end < self.influencer.active_reply_hours_start:
                is_active_hours = current_hour >= self.influencer.active_reply_hours_start or current_hour < self.influencer.active_reply_hours_end
            else:
                is_active_hours = self.influencer.active_reply_hours_start <= current_hour < self.influencer.active_reply_hours_end
            logger.info(f"  - Is within reply active hours: {is_active_hours}")
            if is_active_hours:
                # Ensure client is initialized before processing mentions
                if self.influencer.client is None:
                    if self.influencer.client_init_task is not None:
                        if not self.influencer.client_init_task.done():
                            logger.info(f"Waiting for client initialization for influencer {self.influencer.project_id}")
                            await self.influencer.client_init_task
                    else:
                        logger.info(f"Initializing client for influencer {self.influencer.project_id}")
                        await self.influencer.initialize_client()
                
                if not self.influencer.my_user_id:
                    logger.error(f"Influencer {self.influencer.project_id}: No user ID available for mention processing")
                    return
                logger.info(f"Influencer {self.influencer.project_id}: Processing mentions for user ID {self.influencer.my_user_id}")
                try:
                    # Acquire rate limit slot before fetching mentions
                    if self.influencer.rate_limiter:
                        await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "get_users_mentions")
                    
                    mentions = self.influencer.client.get_users_mentions(
                        id=self.influencer.my_user_id,
                        since_id=self.influencer.last_mention_id,
                        tweet_fields=["in_reply_to_user_id", "author_id", "referenced_tweets", "conversation_id"]
                    )
                    if mentions.data:
                        logger.info(f"Influencer {self.influencer.project_id}: Found {len(mentions.data)} mentions")
                        batch_size = 3
                        for i in range(0, len(mentions.data), batch_size):
                            batch = mentions.data[i:i + batch_size]
                            tasks = [self.process_single_mention(mention) for mention in batch]
                            await asyncio.gather(*tasks)
                            if i + batch_size < len(mentions.data):
                                await asyncio.sleep(5)
                    else:
                        logger.info(f"Influencer {self.influencer.project_id}: No new mentions found")
                except tweepy.TooManyRequests:
                    logger.warning(f"Influencer {self.influencer.project_id}: Rate limit hit while fetching mentions. Will retry later.")
                    return
                except Exception as e:
                    logger.error(f"Influencer {self.influencer.project_id}: Error fetching mentions: {e}")
        except Exception as e:
            logger.error(f"Error processing mentions for influencer {self.influencer.project_id}: {e}")