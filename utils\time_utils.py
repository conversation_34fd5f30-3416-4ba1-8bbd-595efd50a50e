import datetime

def epoch_time_conversion(date_string):
    # Define the format of the input string
    # %a: Abbreviated weekday name (Tue)
    # %b: Abbreviated month name (May)
    # %d: Day of the month as a zero-padded decimal number (06)
    # %H: Hour (24-hour clock) as a zero-padded decimal number (05)
    # %M: Minute as a zero-padded decimal number (32)
    # %S: Second as a zero-padded decimal number (11)
    # %z: UTC offset in the form +HHMM or -HHMM (+0000)
    # %Y: Year with century as a decimal number (2025)
    date_format = "%a %b %d %H:%M:%S %z %Y"

    # Parse the string into a datetime object
    dt_object = datetime.datetime.strptime(date_string, date_format)

    # Convert the datetime object to a Unix timestamp (epoch time)
    epoch_time = int(dt_object.timestamp())

    return epoch_time

def convert_hour_kl_to_utc(hour):
    new_hour = hour - 8
    if new_hour < 0:
        new_hour += 24
    return new_hour
