import json
import asyncmy
from utils.logger_utils import setup_logger

logger = setup_logger(name="database", log_file="log/app.log")

with open("config.json", "r") as file:
    config = json.load(file)


async def create_db_connection():
    """Creates an asynchronous database connection."""
    try:
        mydb = await asyncmy.connect(
            host=config["host"],
            user=config["username"],
            password=config["password"],
            database=config["database"]
        )
        return mydb
    except Exception as e:
        logger.error(f"create_db_connection - Error connecting to the database: {e}")
        return {
            "error": True,
            "message": f"Error connecting to the database: {e}",
            "data": {}
        }


async def save_to_db(table_name='', content={}):
    """Saves a record to the database asynchronously."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return {
            "error": True,
            "message": "Database connection failed.",
            "data": {}
        }

    try:
        columns = tuple(content.keys())
        placeholders = ", ".join(["%s"] * len(columns))
        column_names = ", ".join(columns)
        sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
        values = tuple(content[col] for col in columns)

        async with mydb.cursor() as cursor:
            await cursor.execute(sql, values)
            await mydb.commit()
            return {
            "error": False,
            "message": "Success.",
            "data": {}
        }
    except Exception as e:
        logger.error(f"save_to_db - {table_name} - Error inserting data into database: {e}")
        return {
            "error": True,
            "message": f"save_to_db - {table_name} - {e}",
            "data": {}
        }
    finally:
        await mydb.ensure_closed()


async def get_from_db(table_name, column_name='*', project_id=None, active=None, limit=None, offset=None):
    """Retrieves records from the database asynchronously with optional filtering."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return {
            "error": True,
            "message": "Database connection failed.",
            "data": {}
        }

    try:
        async with mydb.cursor() as cursor:
            where_clauses = []
            if project_id is not None:
                where_clauses.append(f"project_id = {project_id}")
            if active is not None:
                where_clauses.append(f"active = {1 if active else 0}")
            where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"
            if table_name == 'influencer_state':
                sql = f"SELECT {column_name} FROM {table_name} WHERE {where_sql}"
            elif table_name == 'task':
                sql = f"SELECT {column_name} FROM {table_name} WHERE user_id={project_id} ORDER BY timestamp DESC"
            elif table_name == 'replied_tweets':
                sql = f"SELECT {column_name} FROM {table_name} WHERE {where_sql} ORDER BY reply_time DESC"
            else:
                sql = f"SELECT {column_name} FROM {table_name} WHERE {where_sql} ORDER BY timestamp DESC"
            if limit:
                sql += f" LIMIT {limit}"
            if offset:
                sql += f" OFFSET {offset}"
            await cursor.execute(sql)
            records = await cursor.fetchall()
            return records
    except Exception as e:
        logger.error(f"get_from_db - {table_name} - Error getting data from database: {e}")
        return {"status": "failure", "error": str(e), "records": None}
    finally:
        await mydb.ensure_closed()


async def get_tweets_from_db(table_name, column_name, user_id, order='ASC', limit=3):
    """Retrieves the latest records asynchronously."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return []

    try:
        async with mydb.cursor() as cursor:
            sql = f"SELECT {column_name} FROM {table_name} WHERE user_id={user_id} ORDER BY timestamp {order} LIMIT {limit};"
            await cursor.execute(sql)
            records = await cursor.fetchall()
            return records if records else []
    except Exception as e:
        logger.error(f"get_tweets_from_db - Error getting data from database: {e}")
        return []
    finally:
        await mydb.ensure_closed()


async def update_db(table_name, data, where_clause):
    """Updates records in the database asynchronously."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return {
            "error": True,
            "message": "Database connection failed.",
            "data": {}
        }

    try:
        # Build the SET clause
        set_clause = ", ".join([f"{key} = %s" for key in data.keys()])
        values = list(data.values())

        # Build the complete SQL query
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"

        async with mydb.cursor() as cursor:
            await cursor.execute(sql, values)
            await mydb.commit()

            if cursor.rowcount > 0:
                logger.info(f"Successfully updated record in {table_name}")
                return {
                    "error": False,
                    "message": f"Successfully updated record",
                    "data": {}
                }
            else:
                columns = tuple(data.keys())
                placeholders = ", ".join(["%s"] * len(columns))
                column_names = ", ".join(columns)
                logger.warning(f"No records were updated in {table_name}. Creating a new records.")
                sql = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
                await cursor.execute(sql, values)
                await mydb.commit()
                return {
                    "error": False,
                    "message": f"No records found. Created a new records.",
                    "data": {}
                }

    except Exception as e:
        logger.error(f"update_db - {table_name} - Error updating data in database: {e}")
        return {
            "error": True,
            "message": f"Error updating data in database: {e}",
            "data": {}
        }
    finally:
        await mydb.ensure_closed()


async def check_replied_tweet(project_id, tweet_id):
    """Check if a tweet has already been replied to by a specific project."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return {"status": "failure", "error": "Database connection failed"}

    try:
        async with mydb.cursor() as cursor:
            sql = """SELECT id FROM replied_tweets WHERE project_id = %s AND tweet_id = %s"""
            await cursor.execute(sql, (project_id, tweet_id))
            result = await cursor.fetchone()
            return result is not None
    except Exception as e:
        logger.error(f"check_replied_tweet - Error checking replied tweet: {e}")
        return False
    finally:
        await mydb.ensure_closed()


async def count_records_in_table(table_name, project_id=None):
    """Count the total number of records for a given project_id in a table."""
    mydb = await create_db_connection()
    if not mydb:
        logger.error("Failed to connect to the database.")
        return 0
    try:
        async with mydb.cursor() as cursor:
            where_clauses = []
            if project_id is not None:
                where_clauses.append(f"project_id = {project_id}")
            where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"
            sql = f"SELECT COUNT(*) FROM {table_name} WHERE {where_sql}"
            await cursor.execute(sql)
            result = await cursor.fetchone()
            return result[0] if result else 0
    except Exception as e:
        logger.error(f"count_records_in_table - {table_name} - Error counting records: {e}")
        return 0
    finally:
        await mydb.ensure_closed()
