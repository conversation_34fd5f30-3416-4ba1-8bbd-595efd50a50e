# X-Agent Influencer Automation System Documentation

## Overview

**X-Agent** is an influencer automation system designed to post and reply on X (Twitter) using FastAPI, asynchronous database utilities, and modular manager classes. It supports robust logging, error handling, dynamic prompt control for LLM-based posting and replying, and advanced crawling strategies for tweet content.

---

## Architecture

- **FastAPI**: Provides RESTful API endpoints for registration, updating, and log retrieval.
- **Async Database Utilities**: Handles all DB operations asynchronously using `asyncmy` for MySQL.
- **Influencer Manager Classes**: Modular classes orchestrate posting, replying, and state management for each influencer.
- **LLM Integration**: Supports multiple LLM providers for content generation, with dynamic prompt and model selection.
- **Tweet Crawler**: Customizable crawling logic for fetching tweets based on keywords, user profiles, and hashtags.
- **Robust Logging**: All actions and errors are logged to both the database and log files.

---

## Main Components

### 1. `app.py`
- FastAPI app with endpoints:
  - `/register`: Register a new influencer (POST)
  - `/update`: Update influencer settings (POST)
  - `/log`: Retrieve logs with pagination and filtering (POST)
- Manages application lifespan and background influencer management.

### 2. `utils/influencer_manager.py`
- `InfluencerManager`: Loads, manages, and orchestrates all influencer bots.
- `Influencer`: Represents a single influencer, with posting/replying logic, state, and credentials.
- Handles async client initialization, expiry, and active status.

### 3. `utils/influencer_post_manager.py`
- Handles post creation, LLM content generation, premium/non-premium logic, and error handling.
- Summarizes posts for non-premium users if content exceeds 240 characters.

### 4. `utils/influencer_reply_manager.py`
- Handles mention/reply logic, including batching, error handling, and LLM-based reply generation.
- Tracks replied tweets and conversation context.

### 5. `utils/database_utils.py`
- Async DB utilities for CRUD operations, pagination, and record counting.
- Used by all manager classes for state and log persistence.

### 6. `utils/tweets_utils.py`
- Advanced tweet crawling logic with customizable ratios for keyword, user profile, and hashtag.
- Aggregates and saves fetched tweets asynchronously.

### 7. `utils/inference_utils.py`
- LLM API integration and response parsing for multiple providers (OpenAI, Anthropic, xAI, etc).
- Dynamic prompt construction and error handling.

### 8. `utils/x_auth.py`
- Handles authentication with X (Twitter) API using Tweepy.
- Supports user context and credential validation.

### 9. `utils/logger_utils.py`
- Sets up loggers for all modules, writing to `log/app.log`.

### 10. `utils/data_validation.py`
- Pydantic models for request validation (not shown, but referenced in `app.py`).

### 11. `utils/time_utils.py`
- Time conversion utilities for handling time zones and epoch conversions. Used when the server is in UTC time.

---

## Database Schema (Key Tables)

- **register**: Stores influencer credentials, settings, and flags (active, expiry_date, x_premium, etc).
- **created_tweets**: Logs all posts made by influencers.
- **replied_tweets**: Logs all replies/mentions handled by influencers.
- **influencer_state**: Tracks last post and mention IDs for each influencer.
- **tweets**: Stores fetched tweets for context and LLM input.
- **task**: Logs LLM generation tasks and results.

---

## Key Features

- **Async, Non-blocking Design**: All network and DB operations are async, ensuring scalability.
- **Premium/Non-Premium Logic**: Handles X account limitations (e.g., 240 char limit for non-premium).
- **Dynamic LLM Prompting**: Prompts and models are loaded per influencer and can be updated live.
- **Robust Error Handling**: All errors are logged and do not crash the main loop.
- **Flexible Tweet Crawling**: Customizable ratios for keyword, user profile, and hashtag crawling.
- **Immediate Reactivity**: Influencer status (active, expiry) is checked live, not just at startup.
- **Pagination and Filtering**: Log endpoints support pagination and category filtering.

---

## Usage Instructions

1. **Configuration**: Edit `config.json` with your DB and API credentials.
2. **Install Requirements**: `pip install -r requirements.txt`
3. **Run the App**: `python app.py` (or use Uvicorn for production)
4. **Register Influencers**: Use the `/register` endpoint with all required fields.
5. **Update Influencers**: Use the `/update` endpoint to change settings or deactivate.
6. **Monitor Logs**: Use the `/log` endpoint for paginated logs of posts and replies.

---

## API Endpoints

### `/register` (POST)
- Registers a new influencer.
- Requires all credentials, posting/replying settings, and flags.

### `/update` (POST)
- Updates an existing influencer's settings.
- Triggers a reload of influencer state.

### `/log` (POST)
- Retrieves logs for posts, replies, or both.
- Supports pagination and category filtering.

---

## Extension Points

- **Add New LLM Providers**: Extend `set_inference` and `process_response` in `inference_utils.py`.
- **Custom Tweet Crawling**: Adjust logic in `tweets_utils.py` for new crawling strategies.
- **New Features**: Add new endpoints to `app.py` and corresponding manager logic.
- **Database Schema**: Add new columns/tables as needed; update DB utilities accordingly.

---

## Developer Notes

- All async code should be awaited or scheduled as a task.
- Always pass parsed JSON (dict) to `process_response`.
- Use the logger utilities for all error and info logging.
- For new DB columns, always specify column names in insert/update queries.
