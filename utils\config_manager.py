"""
Configuration manager for Twitter client caching system.
Handles loading, validation, and runtime updates of configuration.
"""

import json
import os
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass
from utils.logger_utils import setup_logger

logger = setup_logger(name="config_manager", log_file="log/app.log")

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0

@dataclass
class TwitterClientCacheConfig:
    """Configuration for Twitter client caching"""
    enabled: bool = True
    max_cache_size: int = 100
    client_ttl_hours: int = 1
    validation_interval_minutes: int = 15
    retry_config: RetryConfig = None
    
    def __post_init__(self):
        if self.retry_config is None:
            self.retry_config = RetryConfig()

class ConfigManager:
    """Manages configuration loading, validation, and runtime updates"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = config_path
        self._config: Dict[str, Any] = {}
        self._lock = threading.RLock()
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file"""
        try:
            with open(self.config_path, "r") as file:
                self._config = json.load(file)
            logger.info(f"Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"Configuration file {self.config_path} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing configuration file: {e}")
            raise
    
    def get_config(self, key: str = None) -> Any:
        """Get configuration value by key or entire config if no key provided"""
        with self._lock:
            if key is None:
                return self._config.copy()
            return self._config.get(key)
    
    def get_twitter_cache_config(self) -> TwitterClientCacheConfig:
        """Get Twitter client cache configuration with validation"""
        with self._lock:
            cache_config = self._config.get("twitter_client_cache", {})
            
            # Validate and set defaults
            retry_config_data = cache_config.get("retry_config", {})
            retry_config = RetryConfig(
                max_attempts=retry_config_data.get("max_attempts", 3),
                base_delay=retry_config_data.get("base_delay", 1.0),
                max_delay=retry_config_data.get("max_delay", 60.0)
            )
            
            config = TwitterClientCacheConfig(
                enabled=cache_config.get("enabled", True),
                max_cache_size=cache_config.get("max_cache_size", 100),
                client_ttl_hours=cache_config.get("client_ttl_hours", 1),
                validation_interval_minutes=cache_config.get("validation_interval_minutes", 15),
                retry_config=retry_config
            )
            
            # Validate configuration values
            self._validate_cache_config(config)
            return config
    
    def _validate_cache_config(self, config: TwitterClientCacheConfig) -> None:
        """Validate Twitter client cache configuration"""
        if config.max_cache_size <= 0:
            raise ValueError("max_cache_size must be positive")
        
        if config.client_ttl_hours <= 0:
            raise ValueError("client_ttl_hours must be positive")
        
        if config.validation_interval_minutes <= 0:
            raise ValueError("validation_interval_minutes must be positive")
        
        if config.retry_config.max_attempts <= 0:
            raise ValueError("retry_config.max_attempts must be positive")
        
        if config.retry_config.base_delay <= 0:
            raise ValueError("retry_config.base_delay must be positive")
        
        if config.retry_config.max_delay <= config.retry_config.base_delay:
            raise ValueError("retry_config.max_delay must be greater than base_delay")
    
    def update_config(self, key: str, value: Any, persist: bool = True) -> None:
        """Update configuration value at runtime"""
        with self._lock:
            # Update in-memory configuration
            keys = key.split('.')
            config_ref = self._config
            
            # Navigate to the parent of the target key
            for k in keys[:-1]:
                if k not in config_ref:
                    config_ref[k] = {}
                config_ref = config_ref[k]
            
            # Set the value
            config_ref[keys[-1]] = value
            
            # Validate if updating twitter_client_cache
            if keys[0] == "twitter_client_cache":
                try:
                    self.get_twitter_cache_config()  # This will validate
                except ValueError as e:
                    # Rollback the change
                    logger.error(f"Invalid configuration update: {e}")
                    raise
            
            # Persist to file if requested
            if persist:
                self._save_config()
            
            logger.info(f"Configuration updated: {key} = {value}")
    
    def _save_config(self) -> None:
        """Save current configuration to file"""
        try:
            with open(self.config_path, "w") as file:
                json.dump(self._config, file, indent=2)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise
    
    def reload_config(self) -> None:
        """Reload configuration from file"""
        with self._lock:
            self._load_config()
            logger.info("Configuration reloaded from file")
    
    def is_cache_enabled(self) -> bool:
        """Check if Twitter client caching is enabled"""
        try:
            config = self.get_twitter_cache_config()
            return config.enabled
        except Exception:
            logger.warning("Error checking cache enabled status, defaulting to False")
            return False

# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None
_config_lock = threading.Lock()

def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        with _config_lock:
            if _config_manager is None:
                _config_manager = ConfigManager()
    return _config_manager

def get_twitter_cache_config() -> TwitterClientCacheConfig:
    """Convenience function to get Twitter cache configuration"""
    return get_config_manager().get_twitter_cache_config()