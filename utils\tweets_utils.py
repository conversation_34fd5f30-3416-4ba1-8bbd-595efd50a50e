import json
import httpx
import asyncio
from utils.database_utils import save_to_db
from utils.logger_utils import setup_logger
from utils.time_utils import epoch_time_conversion

logger = setup_logger(name="tweets", log_file="log/app.log")

with open("config.json", "r") as file:
    config = json.load(file)

TWITTERAPI_IO_URL = config["twitter_crawl"]["api_url"]
TWITTERAPI_IO_HEADERS = {"X-API-Key": config["twitterapi"]}

def get_all_media_urls(tweet):
    media_urls = []
    media_entities = []
    if "extendedEntities" in tweet and "media" in tweet["extendedEntities"]:
        media_entities = tweet["extendedEntities"]["media"]
    elif "extended_entities" in tweet and "media" in tweet["extended_entities"]:
        media_entities = tweet["extended_entities"]["media"]
    elif "entities" in tweet and "media" in tweet["entities"]:
        media_entities = tweet["entities"]["media"]
    for media in media_entities:
        if media.get("type") == "photo" and "media_url_https" in media:
            media_urls.append(media["media_url_https"])
        elif media.get("type") == "video" and "video_info" in media:
            mp4_variants = [
                v for v in media["video_info"]["variants"]
                if v.get("content_type") == "video/mp4"
            ]
            if mp4_variants:
                mp4_variants = sorted(
                    mp4_variants,
                    key=lambda x: x.get("bitrate", 0),
                    reverse=True
                )
                media_urls.append(mp4_variants[0]["url"])
        elif media.get("type") == "animated_gif" and "video_info" in media:
            mp4_variants = [
                v for v in media["video_info"]["variants"]
                if v.get("content_type") == "video/mp4"
            ]
            if mp4_variants:
                media_urls.append(mp4_variants[0]["url"])
    return ",".join(media_urls)

async def get_inputs(keyword='', user_profile='', hashtag='', combine_logic='OR'):
    """Constructs parameters for a Twitter API search query based on keyword, profile, and/or hashtag."""
    query_parts = []

    # 1. & 2. Identify and Format Provided Criteria
    if keyword and keyword.strip():
        query_parts.append(keyword.strip())

    if user_profile and user_profile.strip():
        user_profile_list = user_profile.split(',')
        user_profile_count = len(user_profile_list)
        # Remove potential leading '@' and format
        for i in range(user_profile_count):
            cleaned_profile = user_profile_list[i].strip().lstrip('@')
            if cleaned_profile: # Ensure it wasn't just '@'
                query_parts.append(f"from:{cleaned_profile}")

    if hashtag and hashtag.strip():
        # Remove potential leading '#' and ensure one is present
        cleaned_hashtag = hashtag.strip().lstrip('#')
        if cleaned_hashtag: # Ensure it wasn't just '#'
            query_parts.append(f"#{cleaned_hashtag}")

    # 4. Handle Empty Input
    if not query_parts:
        logger.info("Warning: No valid search criteria provided (keyword, user_profile, or hashtag).")
        return None # Cannot construct a meaningful query

    # 5. Combine Criteria
    operator = combine_logic.strip().upper()
    if operator not in ['OR', 'AND']:
        logger.info(f"Warning: Invalid combine_logic '{combine_logic}'. Defaulting to 'OR'.")
        operator = 'OR'

    # Joiner string includes spaces
    joiner = f" {operator} "

    if len(query_parts) > 1:
        search_query_base = joiner.join(query_parts)
    else: # Only one criterion
        search_query_base = query_parts[0]

    # 6. Construct API Parameters
    params = {
        'query': f"{search_query_base}",
        'queryType': 'Latest'
    }

    return params


async def fetch_tweets(initial_params, uid='', max_pages=6, table_name='tweets', keyword='', user_profile='', hashtag=''):
    """Fetches tweets using twitterapi.io, handling pagination and custom crawl ratios."""
    if not initial_params:
        logger.error("Error: Initial parameters are missing.")
        return []

    all_tweets_text = []
    all_save_tasks = []

    # Use config ratios
    default_ratios = config["twitter_crawl"]["default_ratios"]

    # Determine crawl strategy
    if keyword and user_profile and hashtag:
        crawl_plan = [
            (user_profile, default_ratios["user_profile"]),
            (keyword, default_ratios["keyword"]),
            (hashtag, default_ratios["hashtag"])
        ]
        logger.info(f"Crawling with ratio: user_profile={default_ratios['user_profile']}, keyword={default_ratios['keyword']}, hashtag={default_ratios['hashtag']}")
        for target, pages in crawl_plan:
            # Build params for each target
            if target == user_profile:
                params = await get_inputs(keyword='', user_profile=user_profile, hashtag='', combine_logic='OR')
            elif target == keyword:
                params = await get_inputs(keyword=keyword, user_profile='', hashtag='', combine_logic='OR')
            elif target == hashtag:
                params = await get_inputs(keyword='', user_profile='', hashtag=hashtag, combine_logic='OR')
            else:
                continue
            if not params:
                continue
            next_page_token = None
            pages_fetched = 0
            current_params = params.copy()
            async with httpx.AsyncClient(timeout=30.0) as client:
                while pages_fetched < pages:
                    pages_fetched += 1
                    if next_page_token:
                        current_params['cursor'] = next_page_token
                    try:
                        response = await client.get(
                            TWITTERAPI_IO_URL,
                            headers=TWITTERAPI_IO_HEADERS,
                            params=current_params
                        )
                        response.raise_for_status()
                        response_json = response.json()
                    except httpx.HTTPStatusError as e:
                        logger.error(f"HTTP Error on page {pages_fetched}: {e.response.status_code} - {e.response.text}")
                        if e.response.status_code == 429:
                            logger.error("Rate limit likely hit. Stopping fetch.")
                        return False, e.response.text
                    except httpx.RequestError as e:
                        logger.error(f"Request Error on page {pages_fetched}: {e}")
                        break
                    except json.JSONDecodeError:
                        logger.error(f"Error decoding JSON on page {pages_fetched}. Response: {response.text}")
                        break
                    tweets_on_page = response_json.get('tweets', [])
                    if not isinstance(tweets_on_page, list):
                        logger.info("Warning: 'tweets' key not found or not a list in response")
                        tweets_on_page = []
                    if not tweets_on_page and pages_fetched == 1:
                        logger.info("No tweets found matching the criteria.")
                        break
                    logger.info(f"Found {len(tweets_on_page)} tweets on page {pages_fetched} for {target}.")
                    for tweet_data in tweets_on_page:
                        created = tweet_data.get('createdAt', 'Unknown Time')
                        created = epoch_time_conversion(created)
                        tweet_text = tweet_data.get('text', '')
                        media_urls = get_all_media_urls(tweet_data)
                        if tweet_text:
                            all_tweets_text.append(tweet_text)
                            content = {"user_id": uid, "message": tweet_text, "media_url": media_urls, "timestamp": created}
                            task = asyncio.create_task(
                                save_to_db(table_name=table_name, content=content)
                            )
                            all_save_tasks.append(task)
                    next_page_token = response_json.get('next_cursor', {})
                    if not next_page_token:
                        break
                    await asyncio.sleep(1)
    elif user_profile and (keyword or hashtag):
        # 4:2 split between user_profile and (keyword+hashtag)
        logger.info(f"Crawling with ratio: user_profile=4, keyword/hashtag=2")
        crawl_plan = []
        crawl_plan.append((user_profile, 4))
        # Distribute 2 pages between keyword and hashtag
        if keyword and hashtag:
            crawl_plan.append((keyword, 1))
            crawl_plan.append((hashtag, 1))
        elif keyword:
            crawl_plan.append((keyword, 2))
        elif hashtag:
            crawl_plan.append((hashtag, 2))
        for target, pages in crawl_plan:
            if target == user_profile:
                params = await get_inputs(keyword='', user_profile=user_profile, hashtag='', combine_logic='OR')
            elif target == keyword:
                params = await get_inputs(keyword=keyword, user_profile='', hashtag='', combine_logic='OR')
            elif target == hashtag:
                params = await get_inputs(keyword='', user_profile='', hashtag=hashtag, combine_logic='OR')
            else:
                continue
            if not params:
                continue
            next_page_token = None
            pages_fetched = 0
            current_params = params.copy()
            async with httpx.AsyncClient(timeout=30.0) as client:
                while pages_fetched < pages:
                    pages_fetched += 1
                    if next_page_token:
                        current_params['cursor'] = next_page_token
                    try:
                        response = await client.get(
                            TWITTERAPI_IO_URL,
                            headers=TWITTERAPI_IO_HEADERS,
                            params=current_params
                        )
                        response.raise_for_status()
                        response_json = response.json()
                    except httpx.HTTPStatusError as e:
                        logger.error(f"HTTP Error on page {pages_fetched}: {e.response.status_code} - {e.response.text}")
                        if e.response.status_code == 429:
                            logger.error("Rate limit likely hit. Stopping fetch.")
                        return False, e.response.text
                    except httpx.RequestError as e:
                        logger.error(f"Request Error on page {pages_fetched}: {e}")
                        break
                    except json.JSONDecodeError:
                        logger.error(f"Error decoding JSON on page {pages_fetched}. Response: {response.text}")
                        break
                    tweets_on_page = response_json.get('tweets', [])
                    if not isinstance(tweets_on_page, list):
                        logger.info("Warning: 'tweets' key not found or not a list in response")
                        tweets_on_page = []
                    if not tweets_on_page and pages_fetched == 1:
                        logger.info("No tweets found matching the criteria.")
                        break
                    logger.info(f"Found {len(tweets_on_page)} tweets on page {pages_fetched} for {target}.")
                    for tweet_data in tweets_on_page:
                        created = tweet_data.get('createdAt', 'Unknown Time')
                        created = epoch_time_conversion(created)
                        tweet_text = tweet_data.get('text', '')
                        media_urls = get_all_media_urls(tweet_data)
                        if tweet_text:
                            all_tweets_text.append(tweet_text)
                            content = {"user_id": uid, "message": tweet_text, "media_url": media_urls, "timestamp": created}
                            task = asyncio.create_task(
                                save_to_db(table_name=table_name, content=content)
                            )
                            all_save_tasks.append(task)
                    next_page_token = response_json.get('next_cursor', {})
                    if not next_page_token:
                        break
                    await asyncio.sleep(1)
    else:
        # Default: crawl up to 6 pages
        next_page_token = None
        pages_fetched = 0
        current_params = initial_params.copy()
        async with httpx.AsyncClient(timeout=30.0) as client:
            while pages_fetched < max_pages:
                pages_fetched += 1
                if next_page_token:
                    current_params['cursor'] = next_page_token
                try:
                    response = await client.get(
                        TWITTERAPI_IO_URL,
                        headers=TWITTERAPI_IO_HEADERS,
                        params=current_params
                    )
                    response.raise_for_status()
                    response_json = response.json()
                except httpx.HTTPStatusError as e:
                    logger.error(f"HTTP Error on page {pages_fetched}: {e.response.status_code} - {e.response.text}")
                    if e.response.status_code == 429:
                        logger.error("Rate limit likely hit. Stopping fetch.")
                    return False, e.response.text
                except httpx.RequestError as e:
                    logger.error(f"Request Error on page {pages_fetched}: {e}")
                    break
                except json.JSONDecodeError:
                    logger.error(f"Error decoding JSON on page {pages_fetched}. Response: {response.text}")
                    break
                tweets_on_page = response_json.get('tweets', [])
                if not isinstance(tweets_on_page, list):
                    logger.info("Warning: 'tweets' key not found or not a list in response")
                    tweets_on_page = []
                if not tweets_on_page and pages_fetched == 1:
                    logger.info("No tweets found matching the criteria.")
                    break
                logger.info(f"Found {len(tweets_on_page)} tweets on page {pages_fetched}.")
                for tweet_data in tweets_on_page:
                    created = tweet_data.get('createdAt', 'Unknown Time')
                    created = epoch_time_conversion(created)
                    tweet_text = tweet_data.get('text', '')
                    media_urls = get_all_media_urls(tweet_data)
                    if tweet_text:
                        all_tweets_text.append(tweet_text)
                        content = {"user_id": uid, "message": tweet_text, "media_url": media_urls, "timestamp": created}
                        task = asyncio.create_task(
                            save_to_db(table_name=table_name, content=content)
                        )
                        all_save_tasks.append(task)
                next_page_token = response_json.get('next_cursor', {})
                if not next_page_token:
                    break
                await asyncio.sleep(1)
    if all_save_tasks:
        logger.info(f"Waiting for {len(all_save_tasks)} DB save tasks to complete...")
        await asyncio.gather(*all_save_tasks)
        logger.info("DB saves complete.")
    logger.info(f"Finished fetching. Total tweets collected: {len(all_tweets_text)}")
    return True, len(all_tweets_text)
