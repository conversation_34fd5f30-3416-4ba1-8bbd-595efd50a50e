"""
Twitter Client Validation and Health Checking

This module provides comprehensive validation and health checking for cached
Twitter client instances, including automatic cache invalidation for failed clients.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, List
from dataclasses import dataclass
from enum import Enum
import tweepy

from utils.logger_utils import setup_logger

logger = setup_logger(name="client_validator", log_file="log/app.log")


class ValidationResult(Enum):
    """Enumeration of possible validation results."""
    VALID = "valid"
    INVALID_AUTH = "invalid_auth"
    INVALID_NETWORK = "invalid_network"
    INVALID_RATE_LIMIT = "invalid_rate_limit"
    INVALID_UNKNOWN = "invalid_unknown"


@dataclass
class ValidationReport:
    """
    Detailed report of client validation results.
    
    Attributes:
        result: The validation result
        error_message: Detailed error message if validation failed
        response_time_ms: Time taken for validation in milliseconds
        user_id: Twitter user ID if validation was successful
        timestamp: When the validation was performed
    """
    result: ValidationResult
    error_message: Optional[str] = None
    response_time_ms: Optional[float] = None
    user_id: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ClientValidator:
    """
    Comprehensive Twitter client validation and health checking.
    
    This class provides methods to validate cached Twitter client instances,
    detect various types of failures, and automatically invalidate failed clients
    from the cache.
    """
    
    def __init__(self, validation_timeout: float = 10.0, max_validation_failures: int = 3):
        """
        Initialize the client validator.
        
        Args:
            validation_timeout: Timeout for validation requests in seconds
            max_validation_failures: Maximum consecutive failures before invalidation
        """
        self.validation_timeout = validation_timeout
        self.max_validation_failures = max_validation_failures
        self._validation_history: Dict[str, List[ValidationReport]] = {}
    
    async def validate_client(self, client: tweepy.Client, 
                            credentials_hash: str = None) -> ValidationReport:
        """
        Validate that a Twitter client is still functional.
        
        This method performs comprehensive health checks on a Twitter client,
        including authentication verification, network connectivity, and
        basic API functionality.
        
        Args:
            client: The Twitter client to validate
            credentials_hash: Optional hash for tracking validation history
            
        Returns:
            ValidationReport: Detailed validation results
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting client validation for hash: {credentials_hash}")
            
            # Perform validation with timeout
            validation_task = self._perform_validation(client)
            report = await asyncio.wait_for(validation_task, timeout=self.validation_timeout)
            
            # Calculate response time
            end_time = datetime.now()
            report.response_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Store validation history if credentials_hash is provided
            if credentials_hash:
                self._store_validation_result(credentials_hash, report)
            
            if report.result == ValidationResult.VALID:
                logger.info(f"Client validation successful (response_time: {report.response_time_ms:.2f}ms, user_id: {report.user_id})")
            else:
                logger.error(f"Client validation failed: {report.result.value} - {report.error_message} (response_time: {report.response_time_ms:.2f}ms)")
            
            return report
            
        except asyncio.TimeoutError:
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            report = ValidationReport(
                result=ValidationResult.INVALID_NETWORK,
                error_message=f"Validation timeout after {self.validation_timeout}s",
                response_time_ms=response_time
            )
            
            if credentials_hash:
                self._store_validation_result(credentials_hash, report)
            
            logger.error(f"Client validation timeout after {self.validation_timeout}s (response_time: {response_time:.2f}ms)")
            return report
            
        except Exception as e:
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            report = ValidationReport(
                result=ValidationResult.INVALID_UNKNOWN,
                error_message=f"Unexpected validation error: {str(e)}",
                response_time_ms=response_time
            )
            
            if credentials_hash:
                self._store_validation_result(credentials_hash, report)
            
            logger.error(f"Unexpected error during client validation: {str(e)} (response_time: {response_time:.2f}ms)")
            return report
    
    async def _perform_validation(self, client: tweepy.Client) -> ValidationReport:
        """
        Perform the actual validation checks on the client.
        
        Args:
            client: The Twitter client to validate
            
        Returns:
            ValidationReport: The validation results
        """
        try:
            # Primary validation: Get user information
            me_response = client.get_me()
            
            if me_response.data:
                user_id = str(me_response.data.id)
                return ValidationReport(
                    result=ValidationResult.VALID,
                    user_id=user_id
                )
            else:
                # Check for specific error types in the response
                error_msg = "No user data returned"
                if me_response.errors:
                    error_details = [str(error) for error in me_response.errors]
                    error_msg = f"API errors: {', '.join(error_details)}"
                
                return ValidationReport(
                    result=ValidationResult.INVALID_AUTH,
                    error_message=error_msg
                )
                
        except tweepy.Unauthorized as e:
            return ValidationReport(
                result=ValidationResult.INVALID_AUTH,
                error_message=f"Authentication failed: {str(e)}"
            )
            
        except tweepy.TooManyRequests as e:
            return ValidationReport(
                result=ValidationResult.INVALID_RATE_LIMIT,
                error_message=f"Rate limit exceeded: {str(e)}"
            )
            
        except ConnectionError as e:
            return ValidationReport(
                result=ValidationResult.INVALID_NETWORK,
                error_message=f"Network/server error: {str(e)}"
            )
            
        except tweepy.TweepyException as e:
            # Determine the type of Tweepy error
            error_str = str(e).lower()
            if "unauthorized" in error_str or "authentication" in error_str:
                result = ValidationResult.INVALID_AUTH
            elif "rate limit" in error_str or "too many requests" in error_str:
                result = ValidationResult.INVALID_RATE_LIMIT
            elif "network" in error_str or "connection" in error_str:
                result = ValidationResult.INVALID_NETWORK
            else:
                result = ValidationResult.INVALID_UNKNOWN
            
            return ValidationReport(
                result=result,
                error_message=f"Tweepy error: {str(e)}"
            )
    
    def _store_validation_result(self, credentials_hash: str, report: ValidationReport) -> None:
        """
        Store validation result in history for tracking consecutive failures.
        
        Args:
            credentials_hash: The credentials hash to store results for
            report: The validation report to store
        """
        if credentials_hash not in self._validation_history:
            self._validation_history[credentials_hash] = []
        
        # Add the new report
        self._validation_history[credentials_hash].append(report)
        
        # Keep only the last 10 validation results to prevent memory growth
        if len(self._validation_history[credentials_hash]) > 10:
            self._validation_history[credentials_hash] = self._validation_history[credentials_hash][-10:]
    
    def should_invalidate_client(self, credentials_hash: str) -> bool:
        """
        Determine if a client should be invalidated based on validation history.
        
        Args:
            credentials_hash: The credentials hash to check
            
        Returns:
            bool: True if the client should be invalidated, False otherwise
        """
        if credentials_hash not in self._validation_history:
            return False
        
        history = self._validation_history[credentials_hash]
        if len(history) < self.max_validation_failures:
            return False
        
        # Check the last N validation results
        recent_results = history[-self.max_validation_failures:]
        
        # If all recent validations failed, invalidate the client
        all_failed = all(report.result != ValidationResult.VALID for report in recent_results)
        
        if all_failed:
            logger.warning(f"Invalidating client due to {self.max_validation_failures} consecutive validation failures: {credentials_hash}")
            return True
        
        return False
    
    def get_validation_history(self, credentials_hash: str) -> List[ValidationReport]:
        """
        Get the validation history for a specific credentials hash.
        
        Args:
            credentials_hash: The credentials hash to get history for
            
        Returns:
            List[ValidationReport]: List of validation reports
        """
        return self._validation_history.get(credentials_hash, [])
    
    def clear_validation_history(self, credentials_hash: str = None) -> None:
        """
        Clear validation history for a specific hash or all hashes.
        
        Args:
            credentials_hash: Optional specific hash to clear, or None to clear all
        """
        if credentials_hash:
            self._validation_history.pop(credentials_hash, None)
            logger.info(f"Cleared validation history for: {credentials_hash}")
        else:
            cleared_count = len(self._validation_history)
            self._validation_history.clear()
            logger.info(f"Cleared all validation history ({cleared_count} clients)")
    
    async def validate_and_invalidate(self, client: tweepy.Client, 
                                    credentials_hash: str,
                                    cache_manager) -> bool:
        """
        Validate a client and automatically invalidate it from cache if needed.
        
        This method combines validation with automatic cache invalidation,
        providing a complete health check and cleanup solution.
        
        Args:
            client: The Twitter client to validate
            credentials_hash: The credentials hash for the client
            cache_manager: The cache manager to remove invalid clients from
            
        Returns:
            bool: True if the client is valid, False if it was invalidated
        """
        report = await self.validate_client(client, credentials_hash)
        
        # If validation failed, check if we should invalidate
        if report.result != ValidationResult.VALID:
            if self.should_invalidate_client(credentials_hash):
                logger.warning(f"Automatically invalidating client after validation failure: {credentials_hash} ({report.result.value})")
                await cache_manager.remove_client(credentials_hash)
                self.clear_validation_history(credentials_hash)
                return False
        
        return report.result == ValidationResult.VALID
    
    def get_validation_stats(self) -> Dict[str, int]:
        """
        Get statistics about validation history.
        
        Returns:
            Dict[str, int]: Dictionary containing validation statistics
        """
        total_validations = sum(len(history) for history in self._validation_history.values())
        total_clients = len(self._validation_history)
        
        # Count results by type
        result_counts = {result.value: 0 for result in ValidationResult}
        for history in self._validation_history.values():
            for report in history:
                result_counts[report.result.value] += 1
        
        return {
            "total_clients_tracked": total_clients,
            "total_validations": total_validations,
            **result_counts
        }


# Global validator instance
_global_validator = ClientValidator()


def get_validator() -> ClientValidator:
    """
    Get the global client validator instance.
    
    Returns:
        ClientValidator: The global validator instance
    """
    return _global_validator