import time
import json
import httpx
import uvicorn
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
import math

# Load config at the top
with open("config.json", "r") as file:
    config = json.load(file)

from utils.logger_utils import setup_logger
# from utils.time_utils import convert_hour_kl_to_utc
from utils.data_validation import CompletionRequest, LogRequest
from utils.database_utils import save_to_db, update_db, get_from_db, count_records_in_table
from utils.influencer_manager import InfluencerManager

def error_status(e):
    logger.error(f"API request failed: {str(e)}")
    return {
        "error": True,
        "message": "Failed to reach the API.",
        "data": {}
    }

def error_request(e):
    logger.error(f"HTTP request error: {str(e)}")
    return {
        "error": True,
        "message": "Service unavailable. Failed to reach API.",
        "data": {}
    }

def error_exception(e):
    logger.error(f"Unexpected error: {str(e)}")
    return {
        "error": True,
        "message": "An internal error occurred. Might caused by no record found in database.",
        "data": {}
    }

logger = setup_logger(name="influencer_app", log_file="log/app.log")
influencer_manager = InfluencerManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for FastAPI application"""
    # Startup
    await influencer_manager.load_influencers()
    # Start the influencer manager in the background
    background_task = asyncio.create_task(influencer_manager.start())

    yield  # This is where FastAPI serves the application

    # Shutdown
    influencer_manager.stop()
    background_task.cancel()
    try:
        await background_task
    except asyncio.CancelledError:
        pass

app = FastAPI(lifespan=lifespan)

@app.post("/register")
async def register_user(request: CompletionRequest):
    # logger.info(f"Request from register: {json.dumps(request.json(), indent=2)}")
    try:
        content = {
            "project_id": request.project_id,
            "x_api_key": request.x_api_key,
            "x_api_secret": request.x_api_secret,
            "x_access_token": request.x_access_token,
            "x_token_secret": request.x_token_secret,
            "x_bearer_token": request.x_bearer_token,
            "x_username": request.x_username,
            "scheduler_to_post": request.scheduler_to_post,
            "active_post_hours_start": request.active_post_hours_start,
            "active_post_hours_end": request.active_post_hours_end,
            "active_reply_hours_start": request.active_reply_hours_start,
            "active_reply_hours_end": request.active_reply_hours_end,
            "keyword": request.keyword,
            "user_profile": request.user_profile,
            "hashtag": request.hashtag,
            "combine_logic": request.combine_logic,
            "model_selection": request.model_selection,
            "llm_api_key": request.llm_api_key,
            "llm_max_tokens": request.llm_max_tokens,
            "llm_temperature": request.llm_temperature,
            "llm_top_p": request.llm_top_p,
            "llm_system_prompt": request.llm_system_prompt,
            "llm_user_prompt": request.llm_user_prompt,
            "timestamp": time.time(),
            "active": 1 if request.active == True else 0,
            "reply_mentions": 1 if request.reply_mentions == True else 0,
            "expiry_date": request.expiry_date,
            "x_premium": 0#1 if request.x_premium == True else 0
        }
        response = await save_to_db(table_name='register', content=content)

        # Reload influencers to include the new one
        await influencer_manager.load_influencers()

        return response

    except httpx.HTTPStatusError as e:
        return error_status(e)
    except httpx.RequestError as e:
        return error_request(e)
    except Exception as e:
        return error_exception(e)

@app.post("/update")
async def update_user(request: CompletionRequest):
    # logger.info(f"Request from update: {json.dumps(request.json(), indent=2)}")
    try:
        content = {
            "project_id": request.project_id,
            "x_api_key": request.x_api_key,
            "x_api_secret": request.x_api_secret,
            "x_access_token": request.x_access_token,
            "x_token_secret": request.x_token_secret,
            "x_bearer_token": request.x_bearer_token,
            "x_username": request.x_username,
            "scheduler_to_post": request.scheduler_to_post,
            "active_post_hours_start": request.active_post_hours_start,
            "active_post_hours_end": request.active_post_hours_end,
            "active_reply_hours_start": request.active_reply_hours_start,
            "active_reply_hours_end": request.active_reply_hours_end,
            "keyword": request.keyword,
            "user_profile": request.user_profile,
            "hashtag": request.hashtag,
            "combine_logic": request.combine_logic,
            "model_selection": request.model_selection,
            "llm_api_key": request.llm_api_key,
            "llm_max_tokens": request.llm_max_tokens,
            "llm_temperature": request.llm_temperature,
            "llm_top_p": request.llm_top_p,
            "llm_system_prompt": request.llm_system_prompt,
            "llm_user_prompt": request.llm_user_prompt,
            "timestamp": time.time(),
            "active": 1 if request.active == True else 0,
            "reply_mentions": 1 if request.reply_mentions == True else 0,
            "expiry_date": request.expiry_date,
            "x_premium": 0#1 if request.x_premium == True else 0
        }
        response = await update_db(table_name='register', data=content, where_clause=f"project_id={request.project_id}")

        # Reload influencers to reflect the changes
        await influencer_manager.load_influencers()

        return response

    except httpx.HTTPStatusError as e:
        return error_status(e)
    except httpx.RequestError as e:
        return error_request(e)
    except Exception as e:
        return error_exception(e)

@app.post("/log")
async def get_log(request: LogRequest):
    try:
        offset = (request.page - 1) * request.page_size

        # Initialize variables
        post_list = []
        reply_list = []
        total_page = 1

        # Handle posts
        if request.category in ['post', 'all']:
            create = await get_from_db(table_name='created_tweets', column_name='tweet_id, message, timestamp, tweets',
                                       project_id=request.project_id, limit=request.page_size, offset=offset)

            if create and not (isinstance(create, dict) and create.get('status') == 'failure'):
                for c in create:
                    post = {
                        "category": 'error' if c[0] == 'error' else 'post',
                        "message": c[1],
                        "tweets": c[3],
                        "timestamp": c[2]
                    }
                    post_list.append(post)

        # Handle replies
        if request.category in ['reply', 'all']:
            reply = await get_from_db(table_name='replied_tweets', column_name='message, user_id, reply_time',
                                      project_id=request.project_id, limit=request.page_size, offset=offset)

            if reply and not (isinstance(reply, dict) and reply.get('status') == 'failure'):
                for r in reply:
                    reply_item = {
                        "category": 'error' if r[2] == 'error' else 'reply',
                        "message": r[0],
                        "reply_to": r[1],
                        "timestamp": r[2]
                    }
                    reply_list.append(reply_item)

        # Calculate pagination based on category
        if request.category == 'post':
            total_records = await count_records_in_table('created_tweets', request.project_id)
            total_page = math.ceil(total_records / request.page_size) if request.page_size > 0 else 1
        elif request.category == 'reply':
            total_records = await count_records_in_table('replied_tweets', request.project_id)
            total_page = math.ceil(total_records / request.page_size) if request.page_size > 0 else 1
        elif request.category == 'all':
            total_created = await count_records_in_table('created_tweets', request.project_id)
            total_replied = await count_records_in_table('replied_tweets', request.project_id)
            total_records = total_created + total_replied
            total_page = math.ceil(total_records / request.page_size) if request.page_size > 0 else 1
        else:
            return {
                "error": True,
                "message": "Invalid category. Must be 'post', 'reply', or 'all'.",
                "data": {}
            }

        # Check if no records found
        if request.category == 'post' and not post_list:
            return {
                "error": False,
                "message": "No post records found.",
                "data": {}
            }
        elif request.category == 'reply' and not reply_list:
            return {
                "error": False,
                "message": "No reply records found.",
                "data": {}
            }
        elif request.category == 'all' and not post_list and not reply_list:
            return {
                "error": False,
                "message": "No records found.",
                "data": {}
            }

        # Prepare response data based on category
        response_data = {
            "page": request.page,
            "page_size": request.page_size,
            "total_page": total_page,
            "category": request.category
        }

        if request.category == 'post':
            response_data["lists"] = post_list
        elif request.category == 'reply':
            response_data["lists"] = reply_list
        else:  # 'all'
            response_data["posts"] = post_list
            response_data["replies"] = reply_list

        return {
            "error": False,
            "message": "Success.",
            "data": response_data
        }

    except httpx.HTTPStatusError as e:
        return error_status(e)
    except httpx.RequestError as e:
        return error_request(e)
    except Exception as e:
        return error_exception(e)

if __name__ == "__main__":
    uvicorn.run(app, host=config["server"]["host"], port=config["server"]["port"])

