import json
import time
import httpx
import asyncio
from typing import List

from utils.logger_utils import setup_logger
from utils.database_utils import save_to_db, get_from_db

# Initialize logger
logger = setup_logger(name="inference", log_file="log/app.log")

# Load configuration from config.json (consider using environment variables in production for sensitive info)
with open("config.json", "r") as file:
    config = json.load(file)

TIMEOUT = config['llm_timeout']

def set_inference(model_selection: int, api_key: str) -> List[str]:
    """Create and return inference configuration."""
    endpoint_url = config["llm_endpoints"]["default"]
    headers = {"Content-Type": "application/json", "Authorization": f"Api-Key {api_key}"}
    model = "qwen"

    if model_selection == 1:
        endpoint_url = config["llm_endpoints"]["openai"]
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        model = "gpt-4.1"
    elif model_selection == 2:
        endpoint_url = config["llm_endpoints"]["anthropic"]
        headers = {"Content-Type": "application/json", "x-api-key": api_key, "anthropic-version": "2023-06-01"}
        model = "claude-3-5-haiku-20241022"
    elif model_selection == 3:
        endpoint_url = config["llm_endpoints"]["google"]
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        model = "gemini-2.0-flash"
    elif model_selection == 4:
        endpoint_url = config["llm_endpoints"]["xai"]
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        model = "grok-3-latest"
    elif model_selection == 5:
        endpoint_url = config["llm_endpoints"]["deepseek"]
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        model = "deepseek-chat"
    return [endpoint_url, headers, model]


def process_response(response_data):
    """Parse LLM API response and return (message, timestamp)."""
    try:
        # --- Error Handling ---

        # Gemini (Google)
        if isinstance(response_data, list) and "error" in response_data[0]:
            error_info = response_data[0]["error"]
            msg = error_info.get("message", "Gemini API Error")
            for detail in error_info.get("details", []):
                if detail.get("@type") == "type.googleapis.com/google.rpc.LocalizedMessage":
                    msg = detail.get("message", msg)
            return msg, 0

        # xAI-style
        if (
            "code" in response_data and
            "error" in response_data and
            isinstance(response_data["error"], str)
        ):
            return response_data["error"], 0

        # OpenAI (standard)
        if "error" in response_data:
            error_info = response_data["error"]
            if isinstance(error_info, str):
                return error_info, 0
            return error_info.get("message", str(error_info)), 0

        # OpenAI (legacy)
        if response_data.get("object") == "error":
            return response_data.get("message", "Unknown LLM error."), 0

        # --- Valid Output Parsing ---

        # Anthropic (Claude)
        if (
            "content" in response_data and
            isinstance(response_data["content"], list)
        ):
            texts = [
                part.get("text", "")
                for part in response_data["content"]
                if part.get("type") == "text"
            ]
            content = "\n".join(texts).strip()
            return content, time.time()

        # OpenAI-style success
        if "choices" in response_data:
            content = response_data['choices'][0]['message']['content']
            created = response_data.get('created', 0)
            return content, created

        # Possibly valid unknown provider format
        if "message" in response_data and "choices" not in response_data:
            return response_data.get("message", "Unknown error format"), time.time()

        # Fallback
        return str(response_data), 0

    except Exception as e:
        error_msg = f"Unexpected error in processing response: {e}"
        logger.error(f"process_response - {error_msg}")
        return error_msg, 0

async def generate_post(user_id: int, llm: list, tweets: str, max_tokens: int, temperature: float,
                        top_p: float, system_prompt: str, prompt: str):
    """Generate text by analyzing tweets and generating trends."""

    if system_prompt == "":
        system_prompt = """You are an engaging X influencer who creates interactive posts. 
        Your goal is to spark conversations, ask questions, and encourage replies. 
        Always write in a friendly, approachable, and slightly informal tone."""

    latest_post = await get_from_db(table_name='created_tweets', column_name='content', project_id=user_id, limit=1)
    # logger.info(f"latest_post - {latest_post[0][0]}")

    if latest_post:
        user_prompt = f"""
        [TWEETS]
        {tweets}
        
        [PREVIOUS_REPLY]
        {latest_post[0][0]}
        
        [INSTRUCTION]
        Generate a new reply that is distinct and insightful.
        Do not repeat any phrases, facts, or tone from the previous reply.
        Focus on providing new perspective, new summary, or novel insight from the tweets.

        [GOAL]
        {prompt}
        """
    else:
        user_prompt = f"""
        [TWEETS]
        {tweets}
        
        [GOAL]
        {prompt}
        """

    # logger.info(f"system prompt - {system_prompt}")
    # logger.info(f"user prompt - {user_prompt}")

    if "anthropic" in llm[0]:
        payload = {
            "model": llm[2],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "stream": False,
            "system": system_prompt,
            "messages": [{"role": "user", "content": user_prompt}]
        }
    else:
        payload = {
            "messages": [{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
            "model": llm[2],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "stream": False
        }

    try:
        async with httpx.AsyncClient(timeout=TIMEOUT) as client:
            response = await client.post(llm[0], headers=llm[1], json=payload)

        logger.info(f"Response from model: {json.dumps(response.json(), indent=2)}")

        text, creation_time = process_response(response.json())

        content = {"user_id": user_id, "message": text, "timestamp": creation_time}

        if creation_time == 0:
            return False, text
        else:
            asyncio.create_task(save_to_db(table_name='task', content=content))
            return True, text

    except httpx.RequestError as e:
        logger.error(f"Error making request to LLM API: {e}")
    except Exception as e:
        logger.error(f"Error generating text for user {user_id}: {e}")
