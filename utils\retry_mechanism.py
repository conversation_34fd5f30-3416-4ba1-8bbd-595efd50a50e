"""
Retry Mechanism with Exponential Backoff

This module provides robust error handling and retry logic with exponential backoff,
jitter, and circuit breaker functionality for Twitter client operations.
"""

import asyncio
import random
import time
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Callable, Any, Optional, Dict, List, Union
from functools import wraps
from enum import Enum

from utils.logger_utils import setup_logger

logger = setup_logger(name="retry_mechanism", log_file="log/app.log")


@dataclass
class RetryConfig:
    """
    Configuration for retry behavior with exponential backoff.
    
    Attributes:
        max_attempts: Maximum number of retry attempts (default: 3)
        base_delay: Base delay in seconds for exponential backoff (default: 1.0)
        max_delay: Maximum delay in seconds to cap exponential growth (default: 60.0)
        exponential_base: Base for exponential calculation (default: 2.0)
        jitter: Whether to add random jitter to delays (default: True)
        jitter_range: Range for jitter as fraction of delay (default: 0.1)
        circuit_breaker_threshold: Number of consecutive failures to trigger circuit breaker (default: 5)
        circuit_breaker_timeout: Time in seconds to keep circuit breaker open (default: 300)
    """
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    jitter_range: float = 0.1
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 300.0  # 5 minutes


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing fast
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class RetryAttempt:
    """
    Information about a single retry attempt.
    
    Attributes:
        attempt_number: The attempt number (1-based)
        delay_seconds: The delay before this attempt
        error: The error that caused the retry
        timestamp: When the attempt was made
    """
    attempt_number: int
    delay_seconds: float
    error: Exception
    timestamp: datetime


class CircuitBreaker:
    """
    Circuit breaker implementation to prevent cascading failures.
    
    The circuit breaker monitors failure rates and temporarily stops
    making requests when the failure rate is too high, allowing the
    downstream service time to recover.
    """
    
    def __init__(self, config: RetryConfig):
        """
        Initialize the circuit breaker.
        
        Args:
            config: Retry configuration containing circuit breaker settings
        """
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.last_success_time: Optional[datetime] = None
    
    def can_execute(self) -> bool:
        """
        Check if the circuit breaker allows execution.
        
        Returns:
            bool: True if execution is allowed, False otherwise
        """
        now = datetime.now()
        
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            # Check if timeout has passed
            if (self.last_failure_time and 
                now - self.last_failure_time > timedelta(seconds=self.config.circuit_breaker_timeout)):
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info("Circuit breaker transitioning from OPEN to HALF_OPEN")
                return True
            return False
        elif self.state == CircuitBreakerState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self) -> None:
        """Record a successful operation."""
        self.failure_count = 0
        self.last_success_time = datetime.now()
        
        if self.state != CircuitBreakerState.CLOSED:
            self.state = CircuitBreakerState.CLOSED
            logger.info(f"Circuit breaker transitioning to CLOSED after successful operation (previous failures: {self.failure_count})")
    
    def record_failure(self) -> None:
        """Record a failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.OPEN
            logger.error(f"Circuit breaker transitioning from HALF_OPEN to OPEN after failure (failures: {self.failure_count})")
        elif (self.state == CircuitBreakerState.CLOSED and 
              self.failure_count >= self.config.circuit_breaker_threshold):
            self.state = CircuitBreakerState.OPEN
            logger.error(f"Circuit breaker OPEN after {self.failure_count} consecutive failures (threshold: {self.config.circuit_breaker_threshold})")
    
    def get_state_info(self) -> Dict[str, Any]:
        """
        Get current circuit breaker state information.
        
        Returns:
            Dict[str, Any]: State information including current state, failure count, etc.
        """
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "last_failure_time": self.last_failure_time.isoformat() if self.last_failure_time else None,
            "last_success_time": self.last_success_time.isoformat() if self.last_success_time else None,
            "threshold": self.config.circuit_breaker_threshold,
            "timeout_seconds": self.config.circuit_breaker_timeout
        }


class RetryManager:
    """
    Manages retry operations with circuit breaker functionality.
    
    This class provides centralized retry management with circuit breaker
    support to prevent cascading failures across the system.
    """
    
    def __init__(self):
        """Initialize the retry manager."""
        self._circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._retry_stats: Dict[str, List[RetryAttempt]] = {}
    
    def get_circuit_breaker(self, key: str, config: RetryConfig) -> CircuitBreaker:
        """
        Get or create a circuit breaker for a specific key.
        
        Args:
            key: Unique identifier for the circuit breaker
            config: Retry configuration
            
        Returns:
            CircuitBreaker: The circuit breaker instance
        """
        if key not in self._circuit_breakers:
            self._circuit_breakers[key] = CircuitBreaker(config)
        return self._circuit_breakers[key]
    
    def record_retry_attempt(self, key: str, attempt: RetryAttempt) -> None:
        """
        Record a retry attempt for statistics.
        
        Args:
            key: Unique identifier for the operation
            attempt: The retry attempt information
        """
        if key not in self._retry_stats:
            self._retry_stats[key] = []
        
        self._retry_stats[key].append(attempt)
        
        # Keep only the last 100 attempts per key to prevent memory growth
        if len(self._retry_stats[key]) > 100:
            self._retry_stats[key] = self._retry_stats[key][-100:]
    
    def get_retry_stats(self, key: str = None) -> Dict[str, Any]:
        """
        Get retry statistics.
        
        Args:
            key: Optional specific key to get stats for
            
        Returns:
            Dict[str, Any]: Retry statistics
        """
        if key:
            attempts = self._retry_stats.get(key, [])
            return {
                "key": key,
                "total_attempts": len(attempts),
                "recent_attempts": len([a for a in attempts if 
                                      datetime.now() - a.timestamp < timedelta(hours=1)]),
                "average_delay": sum(a.delay_seconds for a in attempts) / len(attempts) if attempts else 0
            }
        else:
            total_attempts = sum(len(attempts) for attempts in self._retry_stats.values())
            return {
                "total_operations": len(self._retry_stats),
                "total_attempts": total_attempts,
                "circuit_breakers": len(self._circuit_breakers)
            }
    
    def clear_stats(self, key: str = None) -> None:
        """
        Clear retry statistics.
        
        Args:
            key: Optional specific key to clear, or None to clear all
        """
        if key:
            self._retry_stats.pop(key, None)
            self._circuit_breakers.pop(key, None)
        else:
            self._retry_stats.clear()
            self._circuit_breakers.clear()


# Global retry manager instance
_global_retry_manager = RetryManager()


def get_retry_manager() -> RetryManager:
    """
    Get the global retry manager instance.
    
    Returns:
        RetryManager: The global retry manager
    """
    return _global_retry_manager


def calculate_delay(attempt: int, config: RetryConfig) -> float:
    """
    Calculate the delay for a retry attempt using exponential backoff.
    
    Args:
        attempt: The attempt number (1-based)
        config: Retry configuration
        
    Returns:
        float: Delay in seconds
    """
    # Calculate exponential delay
    delay = config.base_delay * (config.exponential_base ** (attempt - 1))
    
    # Cap at maximum delay
    delay = min(delay, config.max_delay)
    
    # Add jitter if enabled
    if config.jitter:
        jitter_amount = delay * config.jitter_range
        jitter = random.uniform(-jitter_amount, jitter_amount)
        delay = max(0, delay + jitter)
    
    return delay


async def retry_with_backoff(
    func: Callable,
    config: RetryConfig = None,
    circuit_breaker_key: str = None,
    retry_on: Union[Exception, tuple] = Exception,
    *args,
    **kwargs
) -> Any:
    """
    Execute a function with retry logic and exponential backoff.
    
    This function provides comprehensive retry functionality with exponential
    backoff, jitter, and optional circuit breaker support.
    
    Args:
        func: The function to execute
        config: Retry configuration (uses default if None)
        circuit_breaker_key: Optional key for circuit breaker functionality
        retry_on: Exception type(s) to retry on (default: Exception)
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
        
    Returns:
        Any: The result of the function call
        
    Raises:
        Exception: The last exception if all retry attempts fail
    """
    if config is None:
        config = RetryConfig()
    
    retry_manager = get_retry_manager()
    circuit_breaker = None
    
    if circuit_breaker_key:
        circuit_breaker = retry_manager.get_circuit_breaker(circuit_breaker_key, config)
    
    last_exception = None
    retry_attempts = []
    
    for attempt in range(1, config.max_attempts + 1):
        # Check circuit breaker before attempting
        if circuit_breaker and not circuit_breaker.can_execute():
            logger.warning(f"Circuit breaker OPEN, failing fast for key: {circuit_breaker_key}")
            raise Exception(f"Circuit breaker is OPEN for {circuit_breaker_key}")
        
        try:
            start_time = time.time()
            
            # Log attempt start
            if attempt > 1:
                logger.info(f"Retry attempt {attempt}/{config.max_attempts} for function {func.__name__}")
            else:
                logger.debug(f"Initial attempt for function {func.__name__}")
            
            # Execute the function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            execution_time = (time.time() - start_time) * 1000
            logger.debug(f"Function {func.__name__} succeeded on attempt {attempt} "
                        f"(execution time: {execution_time:.2f}ms)")
            
            # Record success in circuit breaker
            if circuit_breaker:
                circuit_breaker.record_success()
            
            return result
            
        except Exception as e:
            last_exception = e
            execution_time = (time.time() - start_time) * 1000
            
            # Check if this exception type should trigger a retry
            if not isinstance(e, retry_on):
                logger.debug(f"Exception {type(e).__name__} not in retry_on types, not retrying")
                raise e
            
            # Record failure in circuit breaker
            if circuit_breaker:
                circuit_breaker.record_failure()
            
            # Calculate delay for next attempt
            if attempt < config.max_attempts:
                delay = calculate_delay(attempt, config)
                
                # Record retry attempt
                retry_attempt = RetryAttempt(
                    attempt_number=attempt,
                    delay_seconds=delay,
                    error=e,
                    timestamp=datetime.now()
                )
                retry_attempts.append(retry_attempt)
                
                if circuit_breaker_key:
                    retry_manager.record_retry_attempt(circuit_breaker_key, retry_attempt)
                
                logger.warning(f"Function {func.__name__} failed on attempt {attempt}/{config.max_attempts} "
                              f"with {type(e).__name__}: {str(e)} "
                              f"(execution time: {execution_time:.2f}ms). "
                              f"Retrying in {delay:.2f}s...")
                
                # Wait before next attempt
                await asyncio.sleep(delay)
            else:
                logger.error(f"Function {func.__name__} failed on final attempt {attempt}/{config.max_attempts} "
                           f"with {type(e).__name__}: {str(e)} "
                           f"(execution time: {execution_time:.2f}ms). No more retries.")
    
    # Log final failure summary
    total_attempts = len(retry_attempts) + 1  # +1 for initial attempt
    total_delay = sum(attempt.delay_seconds for attempt in retry_attempts)
    
    logger.error(f"All retry attempts exhausted for function {func.__name__}. "
                f"Total attempts: {total_attempts}, Total delay: {total_delay:.2f}s, "
                f"Final error: {type(last_exception).__name__}: {str(last_exception)}")
    
    # Raise the last exception
    raise last_exception


def retry_decorator(
    config: RetryConfig = None,
    circuit_breaker_key: str = None,
    retry_on: Union[Exception, tuple] = Exception
):
    """
    Decorator for adding retry functionality to functions.
    
    Args:
        config: Retry configuration (uses default if None)
        circuit_breaker_key: Optional key for circuit breaker functionality
        retry_on: Exception type(s) to retry on (default: Exception)
        
    Returns:
        Callable: Decorated function with retry functionality
        
    Example:
        @retry_decorator(RetryConfig(max_attempts=5), circuit_breaker_key="twitter_api")
        async def create_twitter_client():
            # Function implementation
            pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await retry_with_backoff(
                func, config, circuit_breaker_key, retry_on, *args, **kwargs
            )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, we need to run in an event loop
            try:
                loop = asyncio.get_running_loop()
                # If we're already in an event loop, we can't use run_until_complete
                # Instead, we'll create a task and run it synchronously
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        asyncio.run,
                        retry_with_backoff(func, config, circuit_breaker_key, retry_on, *args, **kwargs)
                    )
                    return future.result()
            except RuntimeError:
                # No event loop running, we can create one
                return asyncio.run(
                    retry_with_backoff(func, config, circuit_breaker_key, retry_on, *args, **kwargs)
                )
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Convenience functions for common retry scenarios
async def retry_twitter_operation(
    func: Callable,
    credentials_hash: str = None,
    *args,
    **kwargs
) -> Any:
    """
    Retry a Twitter API operation with appropriate configuration.
    
    Args:
        func: The Twitter API function to execute
        credentials_hash: Optional credentials hash for circuit breaker key
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
        
    Returns:
        Any: The result of the function call
    """
    config = RetryConfig(
        max_attempts=3,
        base_delay=1.0,
        max_delay=30.0,
        exponential_base=2.0,
        jitter=True
    )
    
    circuit_breaker_key = f"twitter_api_{credentials_hash}" if credentials_hash else "twitter_api"
    
    # Define Twitter-specific exceptions to retry on
    import tweepy
    twitter_retry_exceptions = (
        tweepy.TooManyRequests,
        ConnectionError,
        TimeoutError,
        tweepy.ServerError
    )
    
    return await retry_with_backoff(
        func, config, circuit_breaker_key, twitter_retry_exceptions, *args, **kwargs
    )


async def retry_client_creation(
    func: Callable,
    credentials_hash: str,
    *args,
    **kwargs
) -> Any:
    """
    Retry Twitter client creation with appropriate configuration.
    
    Args:
        func: The client creation function to execute
        credentials_hash: Credentials hash for circuit breaker key
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
        
    Returns:
        Any: The result of the function call
    """
    config = RetryConfig(
        max_attempts=3,
        base_delay=2.0,
        max_delay=60.0,
        exponential_base=2.0,
        jitter=True,
        circuit_breaker_threshold=3,
        circuit_breaker_timeout=300.0
    )
    
    circuit_breaker_key = f"client_creation_{credentials_hash}"
    
    return await retry_with_backoff(
        func, config, circuit_breaker_key, Exception, *args, **kwargs
    )