"""
Authenticates with X API v2 with client caching support.
"""

import asyncio
import time
from datetime import datetime
from typing import Optional
import tweepy

from utils.logger_utils import setup_logger
from utils.twitter_client_cache import get_cache, generate_credentials_hash
from utils.retry_mechanism import retry_client_creation, retry_twitter_operation
from utils.rate_limiter import get_rate_limit_pool

logger = setup_logger(name="tweets", log_file="log/app.log")


def v2_user(x_api_key, x_api_secret, x_access_token, x_token_secret, x_bearer_token):
    """
    User context (posting, DMs, etc.) with client caching support.
    
    This function maintains backward compatibility while adding client caching
    to improve performance and reduce unnecessary authentication calls.
    
    Args:
        x_api_key: Twitter API key
        x_api_secret: Twitter API secret
        x_access_token: Twitter access token
        x_token_secret: Twitter token secret
        x_bearer_token: Twitter bearer token
        
    Returns:
        tweepy.Client: Authenticated Twitter client instance
        
    Raises:
        Exception: If authentication fails after all retry attempts
    """
    start_time = time.time()
    
    # Generate credentials hash for caching
    credentials_hash = generate_credentials_hash(
        x_api_key, x_api_secret, x_access_token, x_token_secret, x_bearer_token
    )
    
    logger.debug(f"v2_user called for API key: {x_api_key[:10]}...")
    
    try:
        # Try to get the current event loop
        try:
            loop = asyncio.get_running_loop()
            # We're in an async context, use async implementation
            # Create a new task to ensure we're in the right context
            async def _get_client():
                return await _v2_user_async(x_api_key, x_api_secret, x_access_token, 
                                          x_token_secret, x_bearer_token, credentials_hash)
            
            # Run in the current loop
            result = asyncio.run_coroutine_threadsafe(_get_client(), loop).result()
        except RuntimeError:
            # No event loop running, create one
            async def _get_client():
                return await _v2_user_async(x_api_key, x_api_secret, x_access_token, 
                                          x_token_secret, x_bearer_token, credentials_hash)
            
            result = asyncio.run(_get_client())
        
        execution_time = (time.time() - start_time) * 1000
        logger.debug(f"v2_user completed successfully (execution time: {execution_time:.2f}ms)")
        return result
        
    except Exception as e:
        execution_time = (time.time() - start_time) * 1000
        logger.error(f"v2_user failed after {execution_time:.2f}ms: {str(e)}")
        raise


async def _v2_user_async(x_api_key: str, x_api_secret: str, x_access_token: str, 
                        x_token_secret: str, x_bearer_token: str, 
                        credentials_hash: str) -> tweepy.Client:
    """
    Async implementation of v2_user with caching support.
    
    Args:
        x_api_key: Twitter API key
        x_api_secret: Twitter API secret
        x_access_token: Twitter access token
        x_token_secret: Twitter token secret
        x_bearer_token: Twitter bearer token
        credentials_hash: Pre-computed credentials hash
        
    Returns:
        tweepy.Client: Authenticated Twitter client instance
    """
    # Get cache instance (this will now handle event loop compatibility)
    cache = get_cache()
    start_time = time.time()
    
    logger.debug(f"Starting async client retrieval for API key: {x_api_key[:10]}...")
    
    # Define client creation function for retry mechanism
    def create_client() -> tweepy.Client:
        """Create a new Twitter client with authentication validation."""
        creation_start = time.time()
        logger.debug("Creating new Twitter client")
        
        try:
            # Create the client with rate limiting enabled
            client_v2_user_context = tweepy.Client(
                consumer_key=x_api_key,
                consumer_secret=x_api_secret,
                access_token=x_access_token,
                access_token_secret=x_token_secret,
                bearer_token=x_bearer_token,
                wait_on_rate_limit=True
            )
            
            # Test authentication by fetching own user ID
            logger.debug("Testing authentication for new client")
            me_response = client_v2_user_context.get_me()
            
            if me_response.data:
                creation_time = (time.time() - creation_start) * 1000
                user_id = me_response.data.id
                logger.info(f"Created new Twitter client for user {user_id} in {creation_time:.2f}ms")
                return client_v2_user_context
            else:
                error_msg = f"Authentication failed - no user data returned: {me_response.errors}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except tweepy.TweepyException as e:
            creation_time = (time.time() - creation_start) * 1000
            logger.error(f"TweepyException during client creation after {creation_time:.2f}ms: {str(e)}")
            raise
        except Exception as e:
            creation_time = (time.time() - creation_start) * 1000
            logger.error(f"Unexpected error during client creation after {creation_time:.2f}ms: {str(e)}")
            raise
    
    try:
        logger.debug("Attempting to get or create client")
        
        # Use the new fallback-enabled cache method
        client = await cache.get_client_with_fallback(credentials_hash, create_client, validate=True)
        
        # Integrate with rate limiter
        try:
            rate_limiter = await get_rate_limit_pool(x_api_key)
            logger.debug(f"Retrieved rate limiter for API key: {x_api_key[:10]}...")
        except Exception as e:
            logger.error(f"Failed to get rate limiter: {str(e)}")
            # Continue without rate limiter integration - client still has wait_on_rate_limit=True
        
        execution_time = (time.time() - start_time) * 1000
        
        # Log cache decision and performance metrics
        cache_stats = await cache.get_cache_stats()
        logger.debug(f"Successfully retrieved client (execution time: {execution_time:.2f}ms, cache size: {cache_stats['cache_size']})")
        
        return client
        
    except Exception as e:
        execution_time = (time.time() - start_time) * 1000
        logger.error(f"Failed to get client with fallback after {execution_time:.2f}ms: {str(e)}")
        raise