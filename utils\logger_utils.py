import json
import logging
from logging.handlers import TimedRotatingFileHandler

# Load config
with open("config.json", "r") as file:
    config = json.load(file)

def setup_logger(name="app_logger", log_file="app.log"):
    """Setup and return a logger with both file and console handlers."""
    logger = logging.getLogger(name)

    # Set level from config
    log_level = getattr(logging, config["logging"]["level"].upper(), logging.INFO)
    logger.setLevel(log_level)

    # Prevent duplicate log handlers if re-imported
    if logger.hasHandlers():
        return logger

    # File handler with config settings
    file_handler = TimedRotatingFileHandler(
        log_file,
        when=config["logging"]["rotation"],
        backupCount=config["logging"]["backup_count"],
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    # Console handler (for real-time logs)
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger
