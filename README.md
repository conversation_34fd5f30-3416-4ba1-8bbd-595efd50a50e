# x-agent (Influencer)

Access via:

```
ssh -i ssh_keys.txt root@118.107.222.204
```

### Database Setup

1. Login via:

```
mysql -u root -p
```

2. Create database and table using:

```
CREATE DATABASE IF NOT EXISTS bot;
exit;
```

- update config.json with the database credentials
- run `python migrate.py` to create the table

### Start the API

1. Start the python service.

```
pm2 start python --name influencer -- app.py
```

2. Test call the API via `llm3.grafilab.ai/docs`.

```
{
  "project_id": 1002,
  "x_api_key": "ZczsaWwEATbFSprxBLHjCF4Mj",
  "x_api_secret": "hWQgW4tR9EzSw4r7O8BXnCoIdkaIemLzUkeHFuA3peTKTwsn8h",
  "x_access_token": "1789935613900439552-elpofGP7JXeO8cIfuwEd7O4j9TLggr",
  "x_token_secret": "Pq8f0VbR9lAuHXLm6kQoMcTjBgSSCgFo0uGe1t5kks5hU",
  "x_bearer_token": "AAAAAAAAAAAAAAAAAAAAADUZtwEAAAAAEV6QeB9PrB22MCIy%2B4a2zyHXvAM%3DuEmRroXjTrUkNgFzyI0XFbCIz6brE5TuOBpa7xyHYjVEj1QNUy",
  "x_username": "gmgm_grafi",
  "scheduler_to_post": 3,
  "active_post_hours_start": 9,
  "active_post_hours_end": 22,
  "active_reply_hours_start": 11,
  "active_reply_hours_end": 14,
  "keyword": "",
  "user_profile": "Wolfy_XBT, yuyue_chris, 0xcryptowizard, CyberPhilos, cryptocishanjia, 0xSunNFT, brc20niubi, hexiecs, liangfenxiaodao, PolskaCebula, mayangdarana, 0x1JF, 0xleff, BTCdayu, YSI_crypto, monkeyjiang, Vito_168, jiji_eth, HAZENLEE_, huahuayjy",
  "hashtag": "",
  "combine_logic": "OR",
  "model_selection": 1,
  "llm_api_key": "********************************************************************************************************************************************************************",
  "llm_max_tokens": 100,
  "llm_temperature": 0.7,
  "llm_top_p": 0.9,
  "llm_system_prompt": "你是台湾一位 X 推特上的web3大咖博主，主要发些推文吸引关注。你喜欢尬聊和互动，吹嘘赚钱机会和炫富。你也很有耐心和友善回复支持者。你需要了解其他博主的推文和买卖建议，然后总结并发文吸引更多人跟单和吹嘘。",
  "llm_user_prompt": "撰写一条X平台的中文推文。整条推文必须在不超过140个中文字符内完成，内容需自然流畅，并在一个完整句子内表达清晰的观点。不得出现未完成的句子、逗号拖尾、逻辑中断或突兀结尾。无需添加任何称呼语或问候语。请直接输出正文。",
  "active": 1,
  "reply_mentions": 0
}
```
