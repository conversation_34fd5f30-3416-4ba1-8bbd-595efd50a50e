import json
import asyncmy
import asyncio
from utils.logger_utils import setup_logger

logger = setup_logger(name="migration", log_file="log/migration.log")

async def migrate_database():
    """Migrate database to match config.json settings"""

    try:
        # Load config
        with open("config.json", "r") as file:
            config = json.load(file)
        logger.info("Configuration loaded successfully")

        target_db = config.get("database")
        host = config.get("host")
        username = config.get("username")
        password = config.get("password")

        logger.info(f"Target database: {target_db}")

        # Connect without specifying database first
        conn = await asyncmy.connect(
            host=host,
            user=username,
            password=password
        )
        logger.info("Connected to MySQL server")

        async with conn.cursor() as cursor:
            # Check if target database exists
            await cursor.execute(f"SHOW DATABASES LIKE '{target_db}'")
            db_exists = await cursor.fetchone()

            if db_exists:
                # Use target database
                await cursor.execute(f"USE `{target_db}`")
                logger.info(f"Switched to database: {target_db}")

                # Create tweets table
                create_tweets_sql = """
                CREATE TABLE IF NOT EXISTS tweets (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    message LONGTEXT,
                    timestamp bigint(20)
                ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 DEFAULT COLLATE = utf8mb4_unicode_ci
                """
                await cursor.execute(create_tweets_sql)
                logger.info("Created/verified tweets table")

                # Create influencer_state table
                create_influencer_state_sql = """
                CREATE TABLE IF NOT EXISTS influencer_state (
                    project_id INT PRIMARY KEY,
                    last_post_time BIGINT,
                    last_mention_id VARCHAR(255)
                ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 DEFAULT COLLATE = utf8mb4_unicode_ci
                """
                await cursor.execute(create_influencer_state_sql)
                logger.info("Created/verified influencer_state table")

                # Create created_tweets table
                create_created_tweets_sql = """
                CREATE TABLE IF NOT EXISTS created_tweets (
                    id SERIAL PRIMARY KEY,
                    project_id INTEGER,
                    tweet_id TEXT,
                    message TEXT,
                    timestamp BIGINT,
                    content LONGTEXT,
                    tweets LONGTEXT
                ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 DEFAULT COLLATE = utf8mb4_unicode_ci
                """
                await cursor.execute(create_created_tweets_sql)
                logger.info("Created/verified created_tweets table")

                # Create replied_tweets table
                create_replied_tweets_sql = """
                CREATE TABLE IF NOT EXISTS replied_tweets (
                    id SERIAL PRIMARY KEY,
                    project_id INTEGER,
                    tweet_id TEXT,
                    reply_time INTEGER,
                    reply_to TEXT,
                    user_id TEXT,
                    conversation_id TEXT,
                    message TEXT
                ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 DEFAULT COLLATE = utf8mb4_unicode_ci
                """
                await cursor.execute(create_replied_tweets_sql)
                logger.info("Created/verified replied_tweets table")

                # Create register table
                create_register_sql = """
                CREATE TABLE IF NOT EXISTS register (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT,
                    x_api_key LONGTEXT,
                    x_api_secret LONGTEXT,
                    x_access_token LONGTEXT,
                    x_token_secret LONGTEXT,
                    x_bearer_token LONGTEXT,
                    x_username LONGTEXT,
                    scheduler_to_post INT,
                    active_post_hours_start INT,
                    active_post_hours_end INT,
                    active_reply_hours_start INT,
                    active_reply_hours_end INT,
                    keyword TEXT,
                    user_profile TEXT,
                    hashtag TEXT,
                    combine_logic TEXT,
                    model_selection INT,
                    llm_api_key LONGTEXT,
                    llm_max_tokens INT,
                    llm_temperature FLOAT,
                    llm_top_p FLOAT,
                    llm_system_prompt LONGTEXT,
                    llm_user_prompt LONGTEXT,
                    timestamp bigint(20),
                    active INT,
                    reply_mentions INT,
                    expiry_date bigint(20),
                    x_premium INT
                ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 DEFAULT COLLATE = utf8mb4_unicode_ci
                """
                await cursor.execute(create_register_sql)
                logger.info("Created/verified register table")

                # Add column media_url to table tweets
                alter_tweets_sql = """
                ALTER TABLE tweets
                ADD COLUMN media_url LONGTEXT 
                AFTER message
                """
                await cursor.execute(alter_tweets_sql)
                logger.info("Modified/verified tweets table")

                await conn.commit()
                logger.info("Migration completed successfully")
            else:
                logger.warning(f"Database {target_db} does not exist. Migration skipped.")
                print(f"Database {target_db} does not exist. Please create it first.")

    except FileNotFoundError:
        logger.error("config.json file not found")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing config.json: {e}")
        raise
    except asyncmy.MySQLError as e:
        logger.error(f"MySQL error during migration: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during migration: {e}")
        raise
    finally:
        if 'conn' in locals():
            await conn.ensure_closed()
            logger.info("Database connection closed")

if __name__ == "__main__":
    print("Starting database migration...")
    try:
        asyncio.run(migrate_database())
        print("Migration completed successfully! Check log/migration.log for details.")
    except Exception as e:
        print(f"Migration failed: {e}")
        print("Check log/migration.log for detailed error information.")
