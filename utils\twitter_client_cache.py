"""
Twitter Client Caching Infrastructure

This module provides core caching infrastructure for Twitter API clients,
including data models and utility functions for secure credential hashing.
"""

import hashlib
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Optional, Dict, Callable, List
from enum import Enum
import tweepy
import weakref

from utils.logger_utils import setup_logger
from utils.client_validator import ClientValidator, ValidationResult

logger = setup_logger(name="twitter_cache", log_file="log/app.log")


class CacheHealthStatus(Enum):
    """Cache health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    RECOVERING = "recovering"


@dataclass
class CacheHealthReport:
    """
    Report on cache health status and metrics.
    
    Attributes:
        status: Current health status
        error_rate: Recent error rate (0.0 to 1.0)
        corruption_detected: Whether corruption was detected
        last_recovery_attempt: When the last recovery was attempted
        consecutive_failures: Number of consecutive failures
        recovery_success_rate: Success rate of recovery attempts
        recommendations: List of recommended actions
    """
    status: CacheHealthStatus
    error_rate: float = 0.0
    corruption_detected: bool = False
    last_recovery_attempt: Optional[datetime] = None
    consecutive_failures: int = 0
    recovery_success_rate: float = 1.0
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []


@dataclass
class CachedClient:
    """
    Data model for cached Twitter client instances with metadata tracking.
    
    Attributes:
        client: The actual Twitter client instance
        created_at: When the client was cached
        last_used: Last time the client was accessed
        credentials_hash: Hash of the credentials used
        user_id: Twitter user ID for validation
        validation_failures: Number of consecutive validation failures
    """
    client: tweepy.Client
    created_at: datetime
    last_used: datetime
    credentials_hash: str
    user_id: str
    validation_failures: int = 0


def generate_credentials_hash(api_key: str, api_secret: str, 
                            access_token: str, token_secret: str, 
                            bearer_token: str) -> str:
    """
    Generate a secure hash for API credentials.
    
    This function creates a unique identifier for a set of Twitter API credentials
    using SHA-256 hashing. The hash is truncated to 16 characters for readability
    while maintaining sufficient uniqueness for caching purposes.
    
    Args:
        api_key: Twitter API key
        api_secret: Twitter API secret
        access_token: Twitter access token
        token_secret: Twitter token secret
        bearer_token: Twitter bearer token
        
    Returns:
        str: A 16-character hexadecimal hash of the credentials
        
    Example:
        >>> hash_val = generate_credentials_hash("key", "secret", "token", "token_secret", "bearer")
        >>> len(hash_val)
        16
    """
    # Combine all credentials with a delimiter
    combined = f"{api_key}:{api_secret}:{access_token}:{token_secret}:{bearer_token}"
    
    # Generate SHA-256 hash and truncate to 16 characters
    hash_object = hashlib.sha256(combined.encode('utf-8'))
    return hash_object.hexdigest()[:16]


class TwitterClientCache:
    """
    Thread-safe cache manager for Twitter client instances.
    
    This class manages a global cache of authenticated Twitter client instances,
    providing thread-safe operations for storing, retrieving, and validating
    cached clients. It supports parallel processing with per-credentials locking
    to prevent race conditions during client creation.
    """
    
    def __init__(self, max_cache_size: int = 100, client_ttl_hours: int = 1, 
                 validator: ClientValidator = None):
        """
        Initialize the cache with empty storage and synchronization primitives.
        
        Args:
            max_cache_size: Maximum number of clients to cache (default: 100)
            client_ttl_hours: Time to live for cached clients in hours (default: 1)
            validator: Client validator instance (default: creates new instance)
        """
        self._cache: Dict[str, CachedClient] = {}
        self._loop = None
        self._cache_lock = None
        self._creation_locks: Dict[str, asyncio.Lock] = {}
        self._creation_locks_lock = None
        self._pending_creations: Dict[str, asyncio.Future] = {}
        self._max_cache_size = max_cache_size
        self._client_ttl_hours = client_ttl_hours
        self._validator = validator or ClientValidator()
        
        # Fallback and recovery tracking
        self._fallback_enabled = True
        self._consecutive_failures = 0
        self._last_failure_time: Optional[datetime] = None
        self._last_recovery_attempt: Optional[datetime] = None
        self._recovery_attempts = 0
        self._recovery_successes = 0
        self._error_history: List[Dict] = []
        self._health_status = CacheHealthStatus.HEALTHY
        self._corruption_detected = False
    
    def _ensure_loop_compatibility(self):
        """Ensure locks are compatible with the current event loop."""
        current_loop = asyncio.get_running_loop()
        
        # If this is the first time or if we're in a different loop, recreate locks
        if self._loop != current_loop:
            self._loop = current_loop
            self._cache_lock = asyncio.Lock()
            self._creation_locks_lock = asyncio.Lock()
            # Clear creation locks as they're bound to the old loop
            self._creation_locks.clear()
            self._pending_creations.clear()
    
    def _is_expired(self, cached_client: CachedClient) -> bool:
        """
        Check if a cached client has expired based on TTL.
        
        Args:
            cached_client: The cached client to check
            
        Returns:
            bool: True if the client has expired, False otherwise
        """
        ttl_delta = timedelta(hours=self._client_ttl_hours)
        return datetime.now() - cached_client.created_at > ttl_delta
    
    async def _cleanup_expired_clients(self) -> None:
        """Remove expired clients from the cache."""
        expired_keys = []
        for key, cached_client in self._cache.items():
            if self._is_expired(cached_client):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            logger.info(f"Removed expired client from cache: {key}")
    
    async def _enforce_cache_size_limit(self) -> None:
        """Enforce the maximum cache size by removing oldest clients."""
        if len(self._cache) <= self._max_cache_size:
            return
        
        # Sort by last_used timestamp and remove oldest entries
        sorted_items = sorted(
            self._cache.items(),
            key=lambda x: x[1].last_used
        )
        
        num_to_remove = len(self._cache) - self._max_cache_size
        for i in range(num_to_remove):
            key, _ = sorted_items[i]
            del self._cache[key]
            logger.info(f"Removed client from cache due to size limit: {key}")
        
    async def get_client(self, credentials_hash: str, 
                       validate: bool = False) -> Optional[tweepy.Client]:
        """
        Retrieve a cached client instance by credentials hash.
        
        Args:
            credentials_hash: The hash of the credentials to look up
            validate: Whether to validate the client before returning it
            
        Returns:
            Optional[tweepy.Client]: The cached client if found and valid, None otherwise
        """
        self._ensure_loop_compatibility()
        async with self._cache_lock:
            # Clean up expired clients first
            await self._cleanup_expired_clients()
            
            cached_client = self._cache.get(credentials_hash)
            if cached_client:
                # Check if the client has expired
                if self._is_expired(cached_client):
                    del self._cache[credentials_hash]
                    logger.info(f"Removed expired client: {credentials_hash}")
                    return None
                
                # Update last used timestamp
                cached_client.last_used = datetime.now()
                
                # Validate client if requested
                if validate:
                    logger.debug(f"Validating cached client: {credentials_hash}")
                    client_to_validate = cached_client.client
                
                # Return the client (validation will happen outside the lock if needed)
                client = cached_client.client
                
                if validate:
                    # Perform validation outside the lock
                    is_valid = await self.validate_client(client_to_validate, credentials_hash)
                    if not is_valid:
                        logger.warning(f"Cached client validation failed: {credentials_hash}")
                        # Check if we should invalidate based on validation history
                        if self._validator.should_invalidate_client(credentials_hash):
                            await self.remove_client(credentials_hash)
                            return None
                
                logger.debug(f"Cache hit for client: {credentials_hash}")
                self._record_success("get_client", credentials_hash)
                return client
            
            logger.debug(f"Cache miss for client: {credentials_hash}")
            return None
    
    async def set_client(self, credentials_hash: str, client: tweepy.Client) -> None:
        """
        Store a client instance in the cache.
        
        Args:
            credentials_hash: The hash of the credentials
            client: The Twitter client instance to cache
        """
        self._ensure_loop_compatibility()
        async with self._cache_lock:
            # Clean up expired clients first
            await self._cleanup_expired_clients()
            
            # Get user ID for validation purposes
            user_id = "unknown"
            try:
                me_response = client.get_me()
                user_id = str(me_response.data.id) if me_response.data else "unknown"
            except Exception as e:
                logger.error(f"Could not get user ID for cached client: {str(e)}")
            
            now = datetime.now()
            cached_client = CachedClient(
                client=client,
                created_at=now,
                last_used=now,
                credentials_hash=credentials_hash,
                user_id=user_id
            )
            
            self._cache[credentials_hash] = cached_client
            
            # Enforce cache size limits
            await self._enforce_cache_size_limit()
            
            logger.info(f"Cached new client for user {user_id}: {credentials_hash}")
            self._record_success("set_client", credentials_hash)
    
    async def remove_client(self, credentials_hash: str) -> None:
        """
        Remove a client from the cache.
        
        Args:
            credentials_hash: The hash of the credentials to remove
        """
        self._ensure_loop_compatibility()
        async with self._cache_lock:
            if credentials_hash in self._cache:
                cached_client = self._cache[credentials_hash]
                del self._cache[credentials_hash]
                logger.info(f"Removed client from cache: {credentials_hash} (user: {cached_client.user_id})")
    
    async def clear_cache(self) -> None:
        """Clear all cached clients."""
        self._ensure_loop_compatibility()
        async with self._cache_lock:
            cache_size = len(self._cache)
            self._cache.clear()
            logger.info(f"Cleared all cached clients ({cache_size} clients)")
    
    async def validate_client(self, client: tweepy.Client, 
                            credentials_hash: str = None) -> bool:
        """
        Validate that a cached client is still functional using the ClientValidator.
        
        Args:
            client: The Twitter client to validate
            credentials_hash: Optional credentials hash for tracking validation history
            
        Returns:
            bool: True if the client is valid, False otherwise
        """
        report = await self._validator.validate_client(client, credentials_hash)
        return report.result == ValidationResult.VALID
    
    async def validate_and_invalidate_client(self, credentials_hash: str) -> bool:
        """
        Validate a cached client and automatically invalidate it if it fails validation.
        
        This method performs comprehensive health checking and automatically removes
        clients from the cache if they fail validation repeatedly.
        
        Args:
            credentials_hash: The credentials hash of the client to validate
            
        Returns:
            bool: True if the client is valid, False if it was invalidated or not found
        """
        async with self._cache_lock:
            cached_client = self._cache.get(credentials_hash)
            if not cached_client:
                logger.debug(f"No cached client found for validation: {credentials_hash}")
                return False
            
            # Check if client has expired
            if self._is_expired(cached_client):
                del self._cache[credentials_hash]
                logger.info(f"Removed expired client during validation: {credentials_hash}")
                return False
        
        # Validate the client using the validator
        is_valid = await self._validator.validate_and_invalidate(
            cached_client.client, 
            credentials_hash, 
            self
        )
        
        if is_valid:
            # Update last used timestamp for valid clients
            async with self._cache_lock:
                if credentials_hash in self._cache:
                    self._cache[credentials_hash].last_used = datetime.now()
                    self._cache[credentials_hash].validation_failures = 0
        else:
            # Increment validation failure count
            async with self._cache_lock:
                if credentials_hash in self._cache:
                    self._cache[credentials_hash].validation_failures += 1
        
        return is_valid
    
    async def get_or_create_client(self, credentials_hash: str, 
                                 create_func: Callable[[], tweepy.Client]) -> tweepy.Client:
        """
        Get a cached client or create a new one atomically.
        
        This method handles concurrent access by ensuring only one client creation
        occurs per credentials hash, while other requests wait for the result.
        
        Args:
            credentials_hash: The hash of the credentials
            create_func: Function to create a new client if needed
            
        Returns:
            tweepy.Client: The cached or newly created client
        """
        self._ensure_loop_compatibility()
        start_time = datetime.now()
        
        logger.debug(f"get_or_create_client called: {credentials_hash}")
        
        # First, try to get from cache
        client = await self.get_client(credentials_hash)
        if client:
            elapsed = (datetime.now() - start_time).total_seconds() * 1000
            logger.debug(f"Cache hit, returning existing client (took {elapsed:.2f}ms)")
            return client
        
        logger.debug(f"Cache miss, checking for pending creation: {credentials_hash}")
        
        # Check if creation is already in progress
        async with self._creation_locks_lock:
            if credentials_hash in self._pending_creations:
                # Wait for the pending creation to complete
                logger.debug(f"Client creation already in progress, waiting: {credentials_hash}")
                future = self._pending_creations[credentials_hash]
                
                try:
                    result = await future
                    elapsed = (datetime.now() - start_time).total_seconds() * 1000
                    logger.debug(f"Received shared client from pending creation (waited {elapsed:.2f}ms)")
                    return result
                except Exception as e:
                    logger.error(f"Pending creation failed: {str(e)}")
                    raise
            
            # Create a future for this creation
            logger.debug(f"Starting new client creation: {credentials_hash}")
            future = asyncio.Future()
            self._pending_creations[credentials_hash] = future
            
            # Get or create a lock for this credentials hash
            if credentials_hash not in self._creation_locks:
                self._creation_locks[credentials_hash] = asyncio.Lock()
            
            creation_lock = self._creation_locks[credentials_hash]
        
        try:
            async with creation_lock:
                # Double-check cache after acquiring lock
                client = await self.get_client(credentials_hash)
                if client:
                    logger.debug(f"Found client in cache after lock acquisition: {credentials_hash}")
                    future.set_result(client)
                    return client
                
                # Create new client
                creation_start = datetime.now()
                logger.debug(f"Creating new client: {credentials_hash}")
                
                try:
                    new_client = create_func()
                    creation_time = (datetime.now() - creation_start).total_seconds() * 1000
                    logger.debug(f"Client creation completed (took {creation_time:.2f}ms)")
                    
                    # Cache the new client
                    await self.set_client(credentials_hash, new_client)
                    
                    # Notify waiting tasks
                    future.set_result(new_client)
                    elapsed = (datetime.now() - start_time).total_seconds() * 1000
                    logger.debug(f"Successfully created and cached new client (total time {elapsed:.2f}ms)")
                    return new_client
                except Exception as e:
                    creation_time = (datetime.now() - creation_start).total_seconds() * 1000
                    logger.error(f"Client creation failed after {creation_time:.2f}ms: {str(e)}")
                    raise
                
        except Exception as e:
            logger.error(f"Failed to create client: {str(e)}")
            future.set_exception(e)
            raise
        finally:
            # Clean up pending creation tracking
            async with self._creation_locks_lock:
                self._pending_creations.pop(credentials_hash, None)
                
                # Clean up creation lock if no longer needed
                if credentials_hash in self._creation_locks:
                    try:
                        if not self._creation_locks[credentials_hash].locked():
                            del self._creation_locks[credentials_hash]
                    except Exception:
                        pass  # Keep lock for safety
    
    async def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics including validation information.
        
        Returns:
            Dict[str, int]: Dictionary containing cache statistics
        """
        async with self._cache_lock:
            # Calculate validation failure statistics
            total_validation_failures = sum(
                client.validation_failures for client in self._cache.values()
            )
            clients_with_failures = sum(
                1 for client in self._cache.values() if client.validation_failures > 0
            )
            
            cache_stats = {
                "cache_size": len(self._cache),
                "max_cache_size": self._max_cache_size,
                "ttl_hours": self._client_ttl_hours,
                "total_validation_failures": total_validation_failures,
                "clients_with_failures": clients_with_failures
            }
            
            # Add validator statistics
            validator_stats = self._validator.get_validation_stats()
            cache_stats.update(validator_stats)
            
            return cache_stats
    
    def get_validator(self) -> ClientValidator:
        """
        Get the client validator instance used by this cache.
        
        Returns:
            ClientValidator: The validator instance
        """
        return self._validator
    
    def _record_error(self, error: Exception, operation: str, credentials_hash: str = None) -> None:
        """
        Record an error for health monitoring and fallback decisions.
        
        Args:
            error: The error that occurred
            operation: The operation that failed
            credentials_hash: Optional credentials hash
        """
        error_record = {
            "timestamp": datetime.now(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "operation": operation,
            "credentials_hash": credentials_hash
        }
        
        self._error_history.append(error_record)
        
        # Keep only the last 100 errors to prevent memory growth
        if len(self._error_history) > 100:
            self._error_history = self._error_history[-100:]
        
        self._consecutive_failures += 1
        self._last_failure_time = datetime.now()
        
        # Update health status based on error patterns
        self._update_health_status()
        
        logger.error(f"Cache error recorded for operation {operation}: {str(error)}")
    
    def _record_success(self, operation: str, credentials_hash: str = None) -> None:
        """
        Record a successful operation for health monitoring.
        
        Args:
            operation: The operation that succeeded
            credentials_hash: Optional credentials hash
        """
        self._consecutive_failures = 0
        
        # Update health status
        if self._health_status != CacheHealthStatus.HEALTHY:
            old_status = self._health_status
            self._health_status = CacheHealthStatus.HEALTHY
            logger.info(f"Cache health status improved from {old_status.value} to {self._health_status.value}")
    
    def _update_health_status(self) -> None:
        """Update cache health status based on recent errors."""
        if self._consecutive_failures >= 10:
            self._health_status = CacheHealthStatus.FAILED
        elif self._consecutive_failures >= 5:
            self._health_status = CacheHealthStatus.DEGRADED
        elif self._health_status == CacheHealthStatus.RECOVERING and self._consecutive_failures == 0:
            self._health_status = CacheHealthStatus.HEALTHY
    
    def _should_use_fallback(self) -> bool:
        """
        Determine if fallback to non-cached behavior should be used.
        
        Returns:
            bool: True if fallback should be used, False otherwise
        """
        if not self._fallback_enabled:
            return False
        
        # Use fallback if cache is in failed state
        if self._health_status == CacheHealthStatus.FAILED:
            return True
        
        # Use fallback if too many consecutive failures
        if self._consecutive_failures >= 5:
            return True
        
        # Use fallback if corruption is detected
        if self._corruption_detected:
            return True
        
        return False
    
    async def get_client_with_fallback(self, credentials_hash: str, 
                                     create_func: Callable[[], tweepy.Client],
                                     validate: bool = False) -> tweepy.Client:
        """
        Get a cached client with automatic fallback to non-cached behavior.
        
        This method provides the primary interface for getting clients with
        comprehensive error handling, fallback mechanisms, and recovery attempts.
        
        Args:
            credentials_hash: The hash of the credentials
            create_func: Function to create a new client if needed
            validate: Whether to validate the client before returning it
            
        Returns:
            tweepy.Client: The cached or newly created client
            
        Raises:
            Exception: If both cached and fallback approaches fail
        """
        try:
            # Check if we should use fallback immediately
            if self._should_use_fallback():
                logger.debug(f"Using fallback due to cache health status: {self._health_status.value}")
                return await self._fallback_create_client(create_func, credentials_hash)
            
            # Try normal cached approach first
            try:
                client = await self.get_or_create_client(credentials_hash, create_func)
                
                if validate:
                    is_valid = await self.validate_client(client, credentials_hash)
                    if not is_valid:
                        logger.debug(f"Cached client failed validation, attempting fallback: {credentials_hash}")
                        return await self._fallback_create_client(create_func, credentials_hash)
                
                self._record_success("get_client_with_fallback", credentials_hash)
                logger.debug(f"Successfully retrieved client via cache: {credentials_hash}")
                
                return client
                
            except Exception as cache_error:
                self._record_error(cache_error, "get_or_create_client", credentials_hash)
                
                logger.error(f"Cache operation failed, attempting fallback: {str(cache_error)}")
                
                # Fall back to non-cached behavior
                return await self._fallback_create_client(create_func, credentials_hash)
                
        except Exception as e:
            logger.error(f"Both cached and fallback approaches failed: {str(e)}")
            raise
    
    async def _fallback_create_client(self, create_func: Callable[[], tweepy.Client], 
                                    credentials_hash: str) -> tweepy.Client:
        """
        Create a client using fallback (non-cached) behavior.
        
        Args:
            create_func: Function to create a new client
            credentials_hash: The credentials hash for logging
            
        Returns:
            tweepy.Client: The newly created client
        """
        logger.debug(f"Creating client via fallback (non-cached) approach: {credentials_hash}")
        
        try:
            client = create_func()
            logger.debug(f"Fallback client creation successful: {credentials_hash}")
            return client
            
        except Exception as e:
            logger.error(f"Fallback client creation failed: {str(e)}")
            raise
    
    def get_health_report(self) -> CacheHealthReport:
        """
        Get a comprehensive health report for the cache.
        
        Returns:
            CacheHealthReport: Detailed health information and recommendations
        """
        # Calculate error rate from recent history (last hour)
        recent_errors = [
            error for error in self._error_history
            if datetime.now() - error["timestamp"] < timedelta(hours=1)
        ]
        
        total_recent_operations = len(recent_errors) + max(1, len(self._cache))  # Estimate
        error_rate = len(recent_errors) / total_recent_operations if total_recent_operations > 0 else 0.0
        
        # Calculate recovery success rate
        recovery_success_rate = (
            self._recovery_successes / self._recovery_attempts 
            if self._recovery_attempts > 0 else 1.0
        )
        
        # Generate recommendations
        recommendations = []
        if self._health_status == CacheHealthStatus.FAILED:
            recommendations.append("Consider disabling cache temporarily")
            recommendations.append("Check network connectivity and API credentials")
        elif self._health_status == CacheHealthStatus.DEGRADED:
            recommendations.append("Monitor cache performance closely")
            recommendations.append("Consider reducing cache size or TTL")
        elif self._corruption_detected:
            recommendations.append("Run cache corruption repair")
            recommendations.append("Validate cache integrity")
        elif error_rate > 0.1:
            recommendations.append("High error rate detected - investigate recent errors")
        
        if self._consecutive_failures > 3:
            recommendations.append("Multiple consecutive failures - check system health")
        
        return CacheHealthReport(
            status=self._health_status,
            error_rate=error_rate,
            corruption_detected=self._corruption_detected,
            last_recovery_attempt=self._last_recovery_attempt,
            consecutive_failures=self._consecutive_failures,
            recovery_success_rate=recovery_success_rate,
            recommendations=recommendations
        )


# Global cache instance
_global_cache: Optional[TwitterClientCache] = None
_global_cache_lock = None


def get_cache() -> TwitterClientCache:
    """
    Get the global Twitter client cache instance.
    
    Returns:
        TwitterClientCache: The global cache instance
    """
    global _global_cache, _global_cache_lock
    
    # Initialize the global lock if needed
    if _global_cache_lock is None:
        _global_cache_lock = asyncio.Lock()
    
    # Use the lock to ensure thread-safe access to the global cache
    if _global_cache is None:
        _global_cache = TwitterClientCache()
    return _global_cache


def reset_cache() -> TwitterClientCache:
    """
    Reset the global cache instance (useful for testing).
    
    Returns:
        TwitterClientCache: The new global cache instance
    """
    global _global_cache
    _global_cache = TwitterClientCache()
    return _global_cache
