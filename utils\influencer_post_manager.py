import httpx
import json
from datetime import datetime

from utils.database_utils import save_to_db, get_tweets_from_db
from utils.tweets_utils import get_inputs, fetch_tweets
from utils.inference_utils import set_inference, generate_post, process_response
from utils.logger_utils import setup_logger

logger = setup_logger(name="influencer_post_manager", log_file="log/app.log")

# Load config
with open("config.json", "r") as file:
    config = json.load(file)

class InfluencerPostManager:
    def __init__(self, influencer):
        self.influencer = influencer

    async def save_created_tweet(self, tweet_id, message, content=None, error=False, tweets=None):
        await save_to_db(
            table_name='created_tweets',
            content={
                'project_id': self.influencer.project_id,
                'tweet_id': tweet_id if not error else 'error',
                'message': message,
                'timestamp': int(datetime.now().timestamp()),
                'content': content,
                'tweets': tweets
            }
        )

    async def post(self):
        try:
            logger.info(f"Starting post process for influencer {self.influencer.project_id}")
            # Ensure client is initialized
            if self.influencer.client is None:
                if self.influencer.client_init_task is not None:
                    if not self.influencer.client_init_task.done():
                        logger.info(f"Waiting for client initialization for influencer {self.influencer.project_id}")
                        await self.influencer.client_init_task
                else:
                    logger.info(f"Initializing client for influencer {self.influencer.project_id}")
                    await self.influencer.initialize_client()
            
            # Check that client is properly initialized
            if self.influencer.client is None:
                logger.error(f"Client initialization failed for influencer {self.influencer.project_id}")
                raise Exception("Client initialization failed")
            
            # Acquire rate limit slot before making Twitter API calls
            if self.influencer.rate_limiter:
                await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "post")
            
            logger.info(f"Fetching tweets for keyword: {self.influencer.keyword}")
            inputs = await get_inputs(keyword=self.influencer.keyword, user_profile=self.influencer.user_profile, hashtag=self.influencer.hashtag)
            check, text = await fetch_tweets(inputs, self.influencer.project_id)
            if not check:
                return {
                    "error": True,
                    "message": text,  # "No tweets found for the given keyword."
                    "data": {}
                }
            tweets = await get_tweets_from_db(table_name='tweets', column_name='message', user_id=self.influencer.project_id, order='DESC', limit=100)
            logger.info(f"Raw tweets data from DB: {type(tweets)}, data: {tweets[:5] if isinstance(tweets, list) else tweets}")
            # Convert the list of tuples to a list of strings
            tweets_list = [tweet[0] for tweet in tweets] if tweets and not isinstance(tweets, dict) else []
            tweets = set(tweets_list)
            logger.info(f"Fetched {len(tweets) if tweets else 0} unique tweets")
            logger.info(f"Generating post with model {self.influencer.model_selection}")
            llm = set_inference(self.influencer.model_selection, self.influencer.llm_api_key)
            check, llm_reply = await generate_post(
                user_id=self.influencer.project_id,
                llm=llm,
                tweets=str(tweets),
                max_tokens=self.influencer.llm_max_tokens,
                temperature=self.influencer.llm_temperature,
                top_p=self.influencer.llm_top_p,
                system_prompt=self.influencer.llm_system_prompt,
                prompt=self.influencer.llm_user_prompt
            )
            logger.info(f"Generated post content: {llm_reply}")
            if check:
                is_premium = bool(int(self.influencer.x_premium)) if self.influencer.x_premium is not None else False
                response = None
                tweet_posted = False
                if not is_premium:
                    if len(llm_reply) > 240:
                        HEADERS = {"Content-Type": "application/json"}
                        payload = {
                            "model": config['summary_model'],
                            "max_tokens": 90,
                            "temperature": 0.7,
                            "top_p": 0.9,
                            "stream": False,
                            "messages": [{"role": "user", "content": f"Summarize this under 240 characters {llm_reply} "}]
                        }
                        try:
                            async with httpx.AsyncClient(timeout=config['llm_timeout']) as client:
                                response_llm = await client.post(config['llm_endpoints']['default'], headers=HEADERS, json=payload)
                                result = process_response(response_llm.json())
                                if result and isinstance(result, tuple) and result[0]:
                                    llm_reply = result[0]
                                    logger.info(f"Summarized post content to fit 240 chars: {llm_reply}")
                                    logger.info(f"Posting to Twitter - non premium (summarized)")
                                    # Acquire rate limit slot before posting
                                    if self.influencer.rate_limiter:
                                        await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "create_tweet")
                                    response = self.influencer.client.create_tweet(text=llm_reply)
                                    tweet_posted = True
                                else:
                                    logger.error(f"Summarization failed or returned empty. Using original content.")
                        except Exception as e:
                            logger.error(f"Error during summarization: {e}. Using original content.")
                    if not tweet_posted:
                        logger.info(f"Posting to Twitter - non premium (no summarization or fallback)")
                        # Acquire rate limit slot before posting
                        if self.influencer.rate_limiter:
                            await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "create_tweet")
                        response = self.influencer.client.create_tweet(text=llm_reply)
                else:
                    logger.info(f"Posting to Twitter - premium")
                    # Acquire rate limit slot before posting
                    if self.influencer.rate_limiter:
                        await self.influencer.rate_limiter.acquire_request_slot(self.influencer.project_id, "create_tweet")
                    response = self.influencer.client.create_tweet(text=llm_reply)
                if response:
                    logger.info(f"Saving created tweet with tweets data of length: {len(str(tweets))}")
                    await self.save_created_tweet(
                        tweet_id=response.data['id'],
                        message=f"Created a post - https://x.com/{self.influencer.x_username}/status/{response.data['id']}",
                        content=llm_reply,
                        tweets=str(tweets)
                    )
                else:
                    logger.error(f"Failed to post tweet for influencer {self.influencer.project_id}")
                    await self.save_created_tweet(
                        tweet_id='error',
                        message="Failed to post tweet.",
                        content=llm_reply,
                        error=True,
                        tweets=str(tweets)
                    )
            else:
                logger.error(f"Error generated post content: {llm_reply}")
                await self.save_created_tweet(
                    tweet_id='error',
                    message='Error generated post content',
                    content=llm_reply,
                    error=True,
                    tweets=str(tweets)
                )
            self.influencer.last_post_time = datetime.now()
            self.influencer.posts_in_current_interval += 1
            await self.influencer.update_state()
            logger.info(f"Successfully posted and updated state (post {self.influencer.posts_in_current_interval} of {self.influencer.target_posts_in_interval}) for influencer {self.influencer.project_id}")
        except Exception as e:
            logger.error(f"Error posting for influencer {self.influencer.project_id}: {str(e)}")
            raise