import asyncio
import json
from datetime import datetime, timedelta
from utils.logger_utils import setup_logger

logger = setup_logger(name="rate_limiter", log_file="log/app.log")

class SharedRateLimitPool:
    def __init__(self, api_key, max_requests_per_window=300, window_minutes=15):
        self.api_key = api_key
        self.max_requests = max_requests_per_window
        self.window_minutes = window_minutes
        self.request_log = []  # In-memory request tracking
        self.lock = asyncio.Lock()
        
    async def acquire_request_slot(self, project_id, operation_type="general"):
        """Acquire a slot for making an API request"""
        async with self.lock:
            try:
                now = datetime.now()
                window_start = now - timedelta(minutes=self.window_minutes)
                
                # Clean old requests from memory
                self.request_log = [req for req in self.request_log if req['timestamp'] > window_start]
                
                # Check if we have available slots (leave 10% buffer)
                buffer = max(10, int(self.max_requests * 0.1))
                available_slots = self.max_requests - buffer - len(self.request_log)
                
                if available_slots <= 0:
                    # Calculate wait time based on oldest request
                    if self.request_log:
                        oldest_request = min(self.request_log, key=lambda x: x['timestamp'])
                        window_end = oldest_request['timestamp'] + timedelta(minutes=self.window_minutes)
                        wait_time = (window_end - now).seconds
                        
                        if wait_time > 0:
                            logger.info(f"Rate limit reached for API key {self.api_key[:10]}... Project {project_id} waiting {wait_time} seconds")
                            await asyncio.sleep(wait_time + 1)  # Add 1 second buffer
                
                # Log this request in memory
                self.request_log.append({
                    'project_id': project_id,
                    'operation_type': operation_type,
                    'timestamp': now
                })
                
                logger.debug(f"Acquired rate limit slot for project {project_id} with API key {self.api_key[:10]}...")
                return True
                
            except Exception as e:
                logger.error(f"Error acquiring rate limit slot for project {project_id}: {e}")
                # In case of error, we still allow the request but log the issue
                return True
                
    def get_current_usage(self):
        """Get current rate limit usage for monitoring"""
        try:
            now = datetime.now()
            window_start = now - timedelta(minutes=self.window_minutes)
            
            # Count recent requests
            recent_requests = [req for req in self.request_log if req['timestamp'] > window_start]
                
            return {
                'current_requests': len(recent_requests),
                'max_requests': self.max_requests,
                'window_minutes': self.window_minutes,
                'utilization_percent': (len(recent_requests) / self.max_requests) * 100 if self.max_requests > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting rate limit usage: {e}")
            return {
                'current_requests': 0,
                'max_requests': self.max_requests,
                'window_minutes': self.window_minutes,
                'utilization_percent': 0
            }

# Global rate limit pool registry
_rate_limit_pools = {}
_rate_limit_pools_lock = asyncio.Lock()

async def get_rate_limit_pool(api_key, max_requests_per_window=300, window_minutes=15):
    """Get or create a shared rate limit pool for an API key"""
    async with _rate_limit_pools_lock:
        if api_key not in _rate_limit_pools:
            _rate_limit_pools[api_key] = SharedRateLimitPool(
                api_key, 
                max_requests_per_window, 
                window_minutes
            )
        return _rate_limit_pools[api_key]