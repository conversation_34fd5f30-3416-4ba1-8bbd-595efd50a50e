from pydantic import BaseModel, Field

class CompletionRequest(BaseModel):
    project_id: int
    x_api_key: str
    x_api_secret: str
    x_access_token: str
    x_token_secret: str
    x_bearer_token: str
    x_username: str

    scheduler_to_post: int
    active_post_hours_start: int
    active_post_hours_end: int
    active_reply_hours_start: int
    active_reply_hours_end: int

    keyword: str
    user_profile: str
    hashtag: str
    combine_logic: str

    model_selection: int
    llm_api_key: str
    llm_max_tokens: int = Field(ge=1, le=2048, description="Max tokens must be between 1 and 2048")
    llm_temperature: float = Field(default=0.7, ge=0.0, le=1.0, description="Temperature must be between 0 and 1")
    llm_top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="Top-p must be between 0 and 1")
    llm_system_prompt: str
    llm_user_prompt: str

    active: bool
    reply_mentions: bool
    expiry_date: int
    x_premium: int

class LogRequest(BaseModel):
    project_id: int
    category: str
    page: int = 1
    page_size: int = 5