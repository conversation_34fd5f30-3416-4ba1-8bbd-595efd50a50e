import tweepy
import random
import asyncio
import json
from datetime import datetime, time

from utils.x_auth import v2_user
from utils.database_utils import get_from_db, update_db, save_to_db
from utils.inference_utils import set_inference
from utils.logger_utils import setup_logger
from utils.influencer_post_manager import InfluencerPostManager
from utils.influencer_reply_manager import InfluencerReplyManager
from utils.rate_limiter import get_rate_limit_pool

# Load config
with open("config.json", "r") as file:
    config = json.load(file)

logger = setup_logger(name="influencer_manager", log_file="log/app.log")

class InfluencerManager:
    def __init__(self):
        self.influencers = {}
        self.running = False
        self.mention_tasks = {}  # Store mention processing tasks

    async def load_influencers(self):
        """Load all active influencers from the database"""
        logger.info("Loading influencers from database...")
        influencers_data = await get_from_db(
            table_name='register',
            column_name='*',
            active=True
        )

        if isinstance(influencers_data, dict) and influencers_data.get('status') == 'failure':
            logger.error(f"Failed to load influencers: {influencers_data.get('error')}")
            return

        active_count = 0
        for data in influencers_data:
            logger.info(f"Processing influencer data: project_id={data[1]}")
            expiry_date = data[27] if len(data) > 27 else None
            logger.info(f"Influencer {data[1]} expiry_date: {expiry_date} (type: {type(expiry_date)})")

            # Skip influencers with NULL expiry_date (treat as "never expires")
            if expiry_date is None:
                logger.info(f"Influencer {data[1]} has NULL expiry_date - treating as never expires")
            else:
                try:
                    expiry_timestamp = int(expiry_date)
                    if expiry_timestamp < int(datetime.now().timestamp()):
                        logger.info(f"Skipping influencer {data[1]} due to expired expiry_date: {expiry_date}")
                        continue
                except (ValueError, TypeError) as e:
                    logger.error(f"Invalid expiry_date for influencer {data[1]}: {expiry_date} - treating as never expires")
            # Get the last post time and mention ID from the database
            last_post_data = await get_from_db(
                table_name='influencer_state',
                column_name='last_post_time, last_mention_id',
                project_id=data[1]
            )

            last_post_time = None
            last_mention_id = None
            if last_post_data and not isinstance(last_post_data, dict):
                try:
                    # Handle the case where last_post_data might be a tuple of tuples or list of tuples
                    if (isinstance(last_post_data, (list, tuple)) and len(last_post_data) > 0):
                        state_data = last_post_data[0]  # Get the first row
                        if isinstance(state_data, tuple) and len(state_data) >= 2:
                            timestamp = state_data[0]
                            last_post_time = datetime.fromtimestamp(timestamp) if timestamp else None
                            last_mention_id = state_data[1]
                            logger.info(f"Found state data for influencer {data[1]}: last_post_time={last_post_time}, last_mention_id={last_mention_id}")
                        else:
                            logger.warning(f"Unexpected state_data format for influencer {data[1]}: {state_data}")
                    else:
                        logger.warning(f"Unexpected last_post_data format for influencer {data[1]}: {last_post_data}")
                except (IndexError, TypeError) as e:
                    logger.error(f"Error parsing state data for influencer {data[1]}: {e}")
                    last_post_time = None
                    last_mention_id = None

            # Try to get reply_mentions from DB (if present), else default to True
            try:
                reply_mentions = bool(data[26])
            except Exception:
                reply_mentions = False

            # Get shared rate limit pool for this API key
            api_key = data[2]  # x_api_key
            rate_limiter = await get_rate_limit_pool(api_key)

            influencer = Influencer(
                project_id=data[1],
                x_api_key=data[2],
                x_api_secret=data[3],
                x_access_token=data[4],
                x_token_secret=data[5],
                x_bearer_token=data[6],
                x_username=data[7],
                scheduler_to_post=data[8],
                active_post_hours_start=data[9],
                active_post_hours_end=data[10],
                active_reply_hours_start=data[11],
                active_reply_hours_end=data[12],
                keyword=data[13],
                user_profile=data[14],
                hashtag=data[15],
                combine_logic=data[16],
                model_selection=data[17],
                llm_api_key=data[18],
                llm_max_tokens=data[19],
                llm_temperature=data[20],
                llm_top_p=data[21],
                llm_system_prompt=data[22],
                llm_user_prompt=data[23],
                last_post_time=last_post_time,
                last_mention_id=last_mention_id,
                reply_mentions=reply_mentions,
                expiry_date=expiry_date,
                x_premium=data[28],
                rate_limiter=rate_limiter  # Pass the rate limiter
            )
            self.influencers[influencer.project_id] = influencer
            active_count += 1
            logger.info(f"Loaded influencer {influencer.project_id} with schedule: {influencer.scheduler_to_post} hours, active hours: {influencer.active_post_hours_start}-{influencer.active_post_hours_end}")

        logger.info(f"Successfully loaded {active_count} active influencers")

    async def start(self):
        """Start managing all influencers"""
        self.running = True
        logger.info("Starting influencer manager")

        # Initialize last check times for each influencer
        last_post_checks = {}
        last_mention_checks = {}

        while self.running:
            try:
                current_datetime = datetime.now()
                current_time = current_datetime.time()
                logger.info(f"Checking influencers at {current_time.strftime('%H:%M:%S')}")
                logger.info(f"Number of influencers loaded: {len(self.influencers)}")
                logger.info(f"Influencer IDs: {list(self.influencers.keys())}")

                for influencer in list(self.influencers.values()):  # Use list() to avoid modification during iteration
                    logger.info(f"Checking influencer id {influencer.project_id}")
                    influencer_id = influencer.project_id

                    # Check if influencer is still active and not expired
                    if not await self.is_influencer_active(influencer_id):
                        logger.info(f"Influencer {influencer_id} is no longer active or has expired, removing from management")
                        self.remove_influencer(influencer_id)
                        continue

                    # Initialize last check times if not set
                    if influencer_id not in last_post_checks:
                        last_post_checks[influencer_id] = current_datetime
                    if influencer_id not in last_mention_checks:
                        last_mention_checks[influencer_id] = current_datetime

                    # Check if enough time has passed for post check (20-50 minutes)
                    minutes_since_last_post = (current_datetime - last_post_checks[influencer_id]).total_seconds() / 60
                    if minutes_since_last_post >= random.randint(20, 50):
                        logger.info(f"Checking post for influencer {influencer_id}")

                        # Ensure client is initialized before checking should_post
                        if influencer.client is None:
                            if influencer.client_init_task is None:
                                logger.info(f"Influencer {influencer_id}: Starting client initialization")
                                # Add timeout to prevent hanging
                                async def init_with_timeout():
                                    try:
                                        await asyncio.wait_for(influencer.initialize_client(), timeout=30.0)
                                    except asyncio.TimeoutError:
                                        logger.error(f"Influencer {influencer_id}: Client initialization timed out after 30 seconds")
                                        raise
                                influencer.client_init_task = asyncio.create_task(init_with_timeout())
                            elif not influencer.client_init_task.done():
                                logger.info(f"Influencer {influencer_id}: Client initialization in progress, skipping this check")
                                continue
                            else:
                                # Task is done, check if it succeeded
                                try:
                                    # Get the result to check for exceptions
                                    await influencer.client_init_task
                                    if influencer.client is None:
                                        logger.error(f"Influencer {influencer_id}: Client initialization failed - client is None")
                                        # Reset the task so it can be retried next time
                                        influencer.client_init_task = None
                                        continue
                                except Exception as e:
                                    logger.error(f"Influencer {influencer_id}: Client initialization failed with exception: {e}")
                                    # Reset the task so it can be retried next time
                                    influencer.client_init_task = None
                                    continue

                        if influencer.should_post(current_time):
                            logger.info(f"Influencer {influencer_id} should post now")
                            try:
                                await influencer.post_manager.post()
                                logger.info(f"Influencer {influencer_id} completed posting")
                            except Exception as e:
                                logger.error(f"Error in post process for influencer {influencer_id}: {str(e)}")
                                await save_to_db(
                                    table_name='created_tweets',
                                    content={
                                        'project_id': influencer_id,
                                        'tweet_id': 'error',
                                        'message': f"Error creating tweet: {str(e)}",
                                        'timestamp': int(datetime.now().timestamp()),
                                        'content': None
                                    }
                                )
                        else:
                            logger.debug(f"Influencer {influencer_id} should not post now")
                        last_post_checks[influencer_id] = current_datetime

                    # Check if enough time has passed for mention check (15-20 minutes)
                    minutes_since_last_mention = (current_datetime - last_mention_checks[influencer_id]).total_seconds() / 60
                    if minutes_since_last_mention >= random.randint(15, 20):
                        logger.info(f"Checking mentions for influencer {influencer_id}")
                        # Only process mentions if enabled for this influencer
                        if influencer.reply_mentions:
                            # Start mention processing in background if not already running
                            if influencer_id not in self.mention_tasks or self.mention_tasks[influencer_id].done():
                                logger.info(f"Starting mention processing for Influencer {influencer_id}")
                                self.mention_tasks[influencer_id] = asyncio.create_task(
                                    influencer.reply_manager.process_mentions(current_time)
                                )
                        else:
                            logger.info(f"Mentions processing is disabled for Influencer {influencer_id}")
                        last_mention_checks[influencer_id] = current_datetime

                # Sleep for a short time before next iteration
                await asyncio.sleep(60)  # Check every minute for new tasks

            except Exception as e:
                logger.error(f"Error in main loop: {str(e)}")
                await asyncio.sleep(60)  # Wait a minute before retrying if there's an error

    def stop(self):
        """Stop managing influencers"""
        self.running = False
        # Cancel all mention processing tasks
        for task in self.mention_tasks.values():
            if not task.done():
                task.cancel()
        self.mention_tasks.clear()

    def remove_influencer(self, project_id):
        """Remove a specific influencer from management"""
        if project_id in self.influencers:
            # Cancel any running mention tasks for this influencer
            if project_id in self.mention_tasks and not self.mention_tasks[project_id].done():
                self.mention_tasks[project_id].cancel()
                del self.mention_tasks[project_id]

            # Remove the influencer
            del self.influencers[project_id]
            logger.info(f"Removed influencer {project_id} from management")

    async def is_influencer_active(self, project_id):
        """Check if an influencer is still active and not expired"""
        try:
            influencer_data = await get_from_db(
                table_name='register',
                column_name='active, expiry_date',
                project_id=project_id
            )

            if not influencer_data or isinstance(influencer_data, dict):
                return False

            data = influencer_data[0]  # Get the first (and should be only) row
            active = data[0]
            expiry_date = data[1] if len(data) > 1 else None

            logger.info(f"Checking influencer {project_id} - active: {active}, expiry_date: {expiry_date} (type: {type(expiry_date)})")

            # Check if active
            if not active:
                logger.info(f"Influencer {project_id} is not active")
                return False

            # Check if expired (NULL expiry_date means never expires)
            if expiry_date is None:
                logger.info(f"Influencer {project_id} has NULL expiry_date - treating as never expires")
            else:
                try:
                    expiry_timestamp = int(expiry_date)
                    if expiry_timestamp < int(datetime.now().timestamp()):
                        logger.info(f"Influencer {project_id} has expired (expiry_date: {expiry_date})")
                        return False
                except (ValueError, TypeError) as e:
                    logger.error(f"Invalid expiry_date for influencer {project_id}: {expiry_date} - treating as never expires")

            return True

        except Exception as e:
            logger.error(f"Error checking if influencer {project_id} is active: {e}")
            return False


class Influencer:
    def __init__(self, project_id: int, **kwargs):
        self.project_id = project_id
        self.keyword = kwargs.get('keyword')
        self.user_profile = kwargs.get('user_profile')
        self.hashtag = kwargs.get('hashtag')
        self.combine_logic = kwargs.get('combine_logic')
        self.model_selection = kwargs.get('model_selection')
        self.llm_api_key = kwargs.get('llm_api_key')
        self.llm_max_tokens = kwargs.get('llm_max_tokens')
        self.llm_temperature = kwargs.get('llm_temperature')
        self.llm_top_p = kwargs.get('llm_top_p')
        self.llm_system_prompt = kwargs.get('llm_system_prompt')
        self.llm_user_prompt = kwargs.get('llm_user_prompt')
        self.x_api_key = kwargs.get('x_api_key')
        self.x_api_secret = kwargs.get('x_api_secret')
        self.x_access_token = kwargs.get('x_access_token')
        self.x_token_secret = kwargs.get('x_token_secret')
        self.x_bearer_token = kwargs.get('x_bearer_token')
        self.x_username = kwargs.get('x_username')
        self.scheduler_to_post = kwargs.get('scheduler_to_post')
        self.active_post_hours_start = kwargs.get('active_post_hours_start')
        self.active_post_hours_end = kwargs.get('active_post_hours_end')
        self.active_reply_hours_start = kwargs.get('active_reply_hours_start')
        self.active_reply_hours_end = kwargs.get('active_reply_hours_end')
        self.client = None
        self.client_init_task = None
        self.last_post_time = kwargs.get('last_post_time')
        self.last_mention_id = kwargs.get('last_mention_id')
        self.llm = None
        self.posts_in_current_interval = 0
        self.target_posts_in_interval = random.randint(
            config["scheduling"]["target_posts_range"][0], 
            config["scheduling"]["target_posts_range"][1]
        )
        self.interval_start_time = None
        self.my_user_id = None  # Store the user ID after initialization
        self.reply_mentions = kwargs.get('reply_mentions', False)
        self.expiry_date = kwargs.get('expiry_date')
        self.x_premium = kwargs.get('x_premium')
        self.rate_limiter = kwargs.get('rate_limiter')  # Shared rate limiter
        self.post_manager = InfluencerPostManager(self)
        self.reply_manager = InfluencerReplyManager(self)

    async def update_state(self):
        """Update the influencer's state in the database"""
        try:
            last_post_timestamp = int(self.last_post_time.timestamp()) if self.last_post_time else None
            await update_db(
                table_name='influencer_state',
                data={
                    'project_id': self.project_id,
                    'last_post_time': last_post_timestamp,
                    'last_mention_id': self.last_mention_id
                },
                where_clause=f"project_id = {self.project_id}"
            )
        except Exception as e:
            logger.error(f"Error updating state for influencer {self.project_id}: {e}")

    async def initialize_client(self):
        """Asynchronously initialize the Twitter client"""
        if not self.client:
            # Use the async client approach to avoid deadlocks
            try:
                from utils.x_auth import _v2_user_async, generate_credentials_hash

                # Generate credentials hash for caching
                credentials_hash = generate_credentials_hash(
                    self.x_api_key,
                    self.x_api_secret,
                    self.x_access_token,
                    self.x_token_secret,
                    self.x_bearer_token
                )

                logger.info(f"Influencer {self.project_id}: Starting async client initialization")

                self.client = await _v2_user_async(
                    self.x_api_key,
                    self.x_api_secret,
                    self.x_access_token,
                    self.x_token_secret,
                    self.x_bearer_token,
                    credentials_hash
                )

                # Get and store the user ID during initialization
                try:
                    me_response = self.client.get_me()
                    if me_response.data:
                        self.my_user_id = me_response.data.id
                        logger.info(f"Influencer {self.project_id}: Initialized client with user ID {self.my_user_id}")
                    else:
                        logger.error(f"Influencer {self.project_id}: Failed to get user ID during client initialization")
                        self.client = None  # Reset client if we can't get user ID
                except Exception as e:
                    logger.error(f"Influencer {self.project_id}: Error getting user ID during client initialization: {e}")
                    self.client = None  # Reset client if we can't get user ID

            except Exception as e:
                logger.error(f"Influencer {self.project_id}: Failed to initialize Twitter client: {e}")
                self.client = None  # Ensure client is None on failure
                raise

    def initialize_llm(self):
        """Initialize the LLM if not already initialized"""
        if not self.llm:
            self.llm = set_inference(self.model_selection, self.llm_api_key)

    def should_post(self, current_time: time) -> bool:
        logger.info(f"Influencer {self.project_id}: Starting should_post check")
        logger.info(f"  - Current time: {current_time}")
        logger.info(f"  - Active hours: {self.active_post_hours_start}-{self.active_post_hours_end}")
        logger.info(f"  - Scheduler to post: {self.scheduler_to_post} hours")
        logger.info(f"  - Last post time: {self.last_post_time}")
        logger.info(f"  - Posts in current interval: {self.posts_in_current_interval}/{self.target_posts_in_interval}")
        current_hour = current_time.hour
        logger.info(f"  - Current hour: {current_hour}")
        if self.active_post_hours_end < self.active_post_hours_start:
            is_active_hours = current_hour >= self.active_post_hours_start or current_hour < self.active_post_hours_end
            logger.info(f"  - Cross-midnight active hours: {current_hour} is {'within' if is_active_hours else 'outside'} active hours")
        else:
            is_active_hours = self.active_post_hours_start <= current_hour < self.active_post_hours_end
            logger.info(f"  - Normal active hours: {current_hour} is {'within' if is_active_hours else 'outside'} active hours")
        logger.info(f"  - Is within post active hours: {is_active_hours}")
        if not is_active_hours:
            logger.info(f"Influencer {self.project_id}: Not within post active hours, skipping client initialization")
            return False
        logger.info(f"Influencer {self.project_id}: Within active hours, proceeding with scheduling check")

        # Client should already be initialized by the main loop
        if self.client is None or not self.my_user_id:
            logger.error(f"Influencer {self.project_id}: Client not properly initialized")
            return False
            
        current_time_dt = datetime.now()
        logger.info(f"  - Current datetime: {current_time_dt}")
        if self.interval_start_time is None:
            self.interval_start_time = current_time_dt
            self.posts_in_current_interval = 0
            self.target_posts_in_interval = random.randint(
                config["scheduling"]["target_posts_range"][0], 
                config["scheduling"]["target_posts_range"][1]
            )
            logger.info(f"Influencer {self.project_id}: Starting new interval with target of {self.target_posts_in_interval} posts")
        hours_since_interval_start = int((current_time_dt - self.interval_start_time).total_seconds() / 3600)
        logger.info(f"  - Hours since interval start: {hours_since_interval_start}")
        logger.info(f"  - Scheduler interval: {self.scheduler_to_post} hours")
        if hours_since_interval_start >= self.scheduler_to_post:
            self.interval_start_time = current_time_dt
            self.posts_in_current_interval = 0
            self.target_posts_in_interval = random.randint(
                config["scheduling"]["target_posts_range"][0], 
                config["scheduling"]["target_posts_range"][1]
            )
            logger.info(f"Influencer {self.project_id}: Starting new interval with target of {self.target_posts_in_interval} posts")
            return True
        if self.posts_in_current_interval < self.target_posts_in_interval:
            if self.last_post_time:
                minutes_since_last_post = (current_time_dt - self.last_post_time).total_seconds() / 60
                min_delay = config["scheduling"]["min_post_delay_minutes"]
                logger.info(f"  - Minutes since last post: {minutes_since_last_post}")
                logger.info(f"  - Minimum delay required: {min_delay}")
                if minutes_since_last_post >= min_delay:
                    logger.info(f"Influencer {self.project_id}: Should post now (post {self.posts_in_current_interval + 1} of {self.target_posts_in_interval})")
                    return True
                else:
                    logger.info(f"Influencer {self.project_id}: Not enough time since last post")
            else:
                logger.info(f"Influencer {self.project_id}: No previous posts, should post now")
                return True
        logger.info(f"Influencer {self.project_id}: Not time to post yet (reached target of {self.target_posts_in_interval} posts in current interval)")
        return False
